"""
Preprocessing utilities for cleaning and preparing text content for LLM processing.
"""
import re
from utils.logger import logger


def preprocess_impressum_content(content: str) -> str:
    """
    Enhanced preprocessing for Impressum content with intelligent content filtering.
    Focuses on extracting only company-relevant information.

    Args:
        content (str): The raw content of the Impressum page.

    Returns:
        str: Cleaned content with irrelevant parts removed and company info prioritized.
    """
    # Remove script tags and their content
    content = re.sub(
        r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', content, flags=re.IGNORECASE)

    # Remove style tags and their content
    content = re.sub(
        r'<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>', '', content, flags=re.IGNORECASE)

    # Remove HTML comments
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

    # Remove navigation and menu elements (common patterns)
    nav_patterns = [
        r'<nav\b[^>]*>.*?</nav>',
        r'<header\b[^>]*>.*?</header>',
        r'<footer\b[^>]*>.*?</footer>',
        r'<aside\b[^>]*>.*?</aside>',
        r'<div[^>]*class="[^"]*(?:nav|menu|header|footer|sidebar)[^"]*"[^>]*>.*?</div>',
    ]

    for pattern in nav_patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.DOTALL)

    # Remove cookie notices and privacy banners
    cookie_patterns = [
        r'<div[^>]*class="[^"]*(?:cookie|privacy|banner|notice)[^"]*"[^>]*>.*?</div>',
        r'<div[^>]*id="[^"]*(?:cookie|privacy|banner|notice)[^"]*"[^>]*>.*?</div>',
    ]

    for pattern in cookie_patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.DOTALL)

    # Extract sections that likely contain company information
    company_sections = []

    # Look for sections with impressum-related keywords
    impressum_keywords = [
        r'impressum', r'imprint', r'rechtliche[s]?\s+hinweise?', r'angaben',
        r'geschäftsführer', r'inhaber', r'vertreten\s+durch', r'vertretungsberechtigter',
        r'handelsregister', r'registergericht', r'registernummer', r'hrb', r'hra',
        r'steuernummer', r'steuer-nr', r'ust-idnr', r'umsatzsteuer-id', r'ust-id',
        r'gmbh', r'ag', r'e\.k\.', r'kg', r'ohg', r'ug', r'gbr',
        r'anschrift', r'adresse', r'sitz'
    ]

    # Split content into paragraphs and filter for relevant ones
    paragraphs = re.split(
        r'</?(?:p|div|section|article)\b[^>]*>', content, flags=re.IGNORECASE)

    for paragraph in paragraphs:
        # Clean HTML tags from paragraph
        clean_para = re.sub(r'<[^>]*>', ' ', paragraph)
        clean_para = re.sub(r'\s+', ' ', clean_para).strip()

        if len(clean_para) < 20:  # Skip very short paragraphs
            continue

        # Check if paragraph contains company-relevant keywords
        para_lower = clean_para.lower()
        if any(re.search(keyword, para_lower) for keyword in impressum_keywords):
            company_sections.append(clean_para)

    # If we found relevant sections, use them; otherwise fall back to full content
    if company_sections:
        content = '\n\n'.join(company_sections)
        logger.info(
            f"Extracted {len(company_sections)} relevant impressum sections")
    else:
        # Fall back to general cleaning
        content = re.sub(r'<[^>]*>', ' ', content)
        content = re.sub(r'\s+', ' ', content)
        logger.warning(
            "No specific impressum sections found, using full content")

    # Remove very long strings that are likely not relevant (e.g., base64 encoded data)
    content = re.sub(r'\S{100,}', '', content)

    # Remove common non-company phrases
    noise_patterns = [
        r'cookie[s]?\s+(?:policy|notice|banner)',
        r'privacy\s+policy',
        r'terms\s+(?:of\s+)?(?:service|use)',
        r'all\s+rights\s+reserved',
        r'copyright\s+©',
        r'powered\s+by',
        r'website\s+by',
        r'design\s+by',
    ]

    for pattern in noise_patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE)

    # Limit content length but be more generous for impressum content
    max_length = 5000  # Increased from 10000 to focus on most relevant content
    if len(content) > max_length:
        # Try to cut at sentence boundaries
        sentences = re.split(r'[.!?]\s+', content[:max_length])
        if len(sentences) > 1:
            content = '. '.join(sentences[:-1]) + '.'
        else:
            content = content[:max_length]
        logger.info(
            f"Truncated impressum content to {len(content)} characters")

    return content.strip()


def preprocess_html_content(content: str, max_length: int = 10000) -> str:
    """
    General-purpose HTML content preprocessor for LLM input.

    Args:
        content (str): The raw HTML content to preprocess.
        max_length (int): Maximum length of the output content.

    Returns:
        str: Cleaned content suitable for LLM processing.
    """
    # Remove script tags and their content
    content = re.sub(
        r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', content)

    # Remove style tags and their content
    content = re.sub(
        r'<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>', '', content)

    # Remove HTML comments
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)

    # Remove meta tags
    content = re.sub(r'<meta\b[^>]*>', '', content)

    # Remove link tags
    content = re.sub(r'<link\b[^>]*>', '', content)

    # Remove all HTML tags but preserve their content
    content = re.sub(r'<[^>]*>', ' ', content)

    # Replace multiple whitespace with a single space
    content = re.sub(r'\s+', ' ', content)

    # Remove very long strings that are likely not relevant
    content = re.sub(r'\S{100,}', '', content)

    # Limit content length
    if len(content) > max_length:
        content = content[:max_length]

    return content.strip()


def extract_text_from_html(html_content: str) -> str:
    """
    Extracts readable text from HTML content, focusing on main content areas.

    Args:
        html_content (str): The raw HTML content.

    Returns:
        str: Extracted text content.
    """
    # Remove script, style, head, and other non-content elements
    for tag in ['script', 'style', 'head', 'noscript', 'iframe', 'svg']:
        html_content = re.sub(
            f'<{tag}[^>]*>.*?</{tag}>', '', html_content, flags=re.DOTALL)

    # Replace common block elements with newlines for better readability
    for tag in ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'tr', 'br']:
        html_content = re.sub(
            f'</{tag}>', '\n', html_content, flags=re.IGNORECASE)

    # Remove all remaining HTML tags
    html_content = re.sub(r'<[^>]*>', '', html_content)

    # Replace multiple newlines with a single newline
    html_content = re.sub(r'\n+', '\n', html_content)

    # Replace multiple spaces with a single space
    html_content = re.sub(r' +', ' ', html_content)

    return html_content.strip()
