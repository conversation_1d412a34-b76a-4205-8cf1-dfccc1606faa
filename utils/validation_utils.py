from urllib.parse import urlparse


def is_valid_url(url: str) -> bool:
    """
    Checks if a given string is a valid URL.
    A valid URL must have a scheme ('http' or 'https') and a network location (domain).
    """
    try:
        result = urlparse(url)
        # Check for a valid scheme and the presence of a network location.
        return result.scheme in ['http', 'https'] and bool(result.netloc)
    except (ValueError, AttributeError):
        return False