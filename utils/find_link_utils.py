import re
from urllib.parse import urljoin
from bs4 import BeautifulSoup


async def find_impressum_link(homepage_html: str, base_url: str) -> str | None:
    """
    Look for an impressum/legal notice link in the homepage HTML and return the full URL if found.
    Uses BeautifulSoup for robust HTML parsing with enhanced pattern matching.

    Args:
        homepage_html (str): HTML content of the homepage
        base_url (str): Base URL of the website for resolving relative URLs

    Returns:
        The absolute URL to the Impressum page, or None if not found.
    """
    if not homepage_html:
        return None

    # Check for bot detection messages
    bot_indicators = [
        "pardon our interruption",
        "browser made us think you were a bot",
        "cloudflare",
        "access denied",
        "blocked"
    ]

    content_lower = homepage_html.lower()
    if any(indicator in content_lower for indicator in bot_indicators):
        # Try common impressum URL patterns as fallback
        common_patterns = [
            "/impressum",
            "/hilfe/impressum",
            "/imprint",
            "/legal",
            "/legal-notice",
            "/rechtliches",
            "/datenschutz"
        ]

        for pattern in common_patterns:
            potential_url = urljoin(base_url, pattern)
            # Return the first common pattern - caller can test if it works
            return potential_url

    soup = BeautifulSoup(homepage_html, "lxml")

    # Prioritized patterns: most specific first, fallbacks last
    # Primary impressum patterns (highest priority)
    primary_patterns = [
        re.compile(r"impressum", re.IGNORECASE),
        re.compile(r"imprint", re.IGNORECASE),
    ]

    # Secondary legal patterns (medium priority)
    secondary_patterns = [
        re.compile(r"legal[- ]?notice", re.IGNORECASE),
        re.compile(r"rechtliches", re.IGNORECASE),
        re.compile(r"rechtliche[- ]?hinweise", re.IGNORECASE),
    ]

    # Fallback patterns (lowest priority - only if no primary/secondary found)
    fallback_patterns = [
        re.compile(r"agb", re.IGNORECASE),  # Allgemeine Geschäftsbedingungen
        re.compile(r"terms[- ]?of[- ]?service", re.IGNORECASE),
        # Privacy policy - last resort
        re.compile(r"datenschutz", re.IGNORECASE),
        re.compile(r"privacy[- ]?policy", re.IGNORECASE)
    ]

    # Collect all links first, then prioritize
    all_links = []

    # Search in links and collect matches with priority
    for link in soup.find_all("a", href=True):
        link_text = link.get_text(strip=True)
        link_href = link.get("href", "")
        link_title = link.get("title", "")

        # Check primary patterns first
        for pattern in primary_patterns:
            if (pattern.search(link_text) or
                pattern.search(link_href) or
                    pattern.search(link_title)):
                # Return immediately for primary matches
                return urljoin(base_url, link_href)

        # Collect secondary matches
        for pattern in secondary_patterns:
            if (pattern.search(link_text) or
                pattern.search(link_href) or
                    pattern.search(link_title)):
                all_links.append(('secondary', urljoin(base_url, link_href)))

        # Collect fallback matches
        for pattern in fallback_patterns:
            if (pattern.search(link_text) or
                pattern.search(link_href) or
                    pattern.search(link_title)):
                all_links.append(('fallback', urljoin(base_url, link_href)))

    # Return best secondary match if found
    secondary_links = [link for priority,
                       link in all_links if priority == 'secondary']
    if secondary_links:
        return secondary_links[0]

    # Search in common footer/navigation areas with same prioritization
    footer_selectors = ["footer", ".footer", "#footer", "nav", ".nav", "#nav"]
    for selector in footer_selectors:
        elements = soup.select(selector)
        for element in elements:
            for link in element.find_all("a", href=True):
                link_text = link.get_text(strip=True)
                link_href = link.get("href", "")

                # Check primary patterns first in footer
                for pattern in primary_patterns:
                    if pattern.search(link_text) or pattern.search(link_href):
                        return urljoin(base_url, link_href)

                # Collect secondary matches from footer
                for pattern in secondary_patterns:
                    if pattern.search(link_text) or pattern.search(link_href):
                        all_links.append(
                            ('secondary', urljoin(base_url, link_href)))

                # Collect fallback matches from footer
                for pattern in fallback_patterns:
                    if pattern.search(link_text) or pattern.search(link_href):
                        all_links.append(
                            ('fallback', urljoin(base_url, link_href)))

    # Return best secondary match if found (including footer matches)
    secondary_links = [link for priority,
                       link in all_links if priority == 'secondary']
    if secondary_links:
        return secondary_links[0]

    # Return fallback match only if no primary or secondary found
    fallback_links = [link for priority,
                      link in all_links if priority == 'fallback']
    if fallback_links:
        return fallback_links[0]

    return None
