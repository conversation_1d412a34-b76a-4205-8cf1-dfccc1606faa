import logging
import sys
import os
from datetime import datetime

try:
    # Attempt to import the log level from settings.
    from config.settings import LOG_LEVEL
except FileNotFoundError:
    # Fallback for environments where config.ini is not present (e.g., clean test runs).
    print("Warning: config.ini not found. Defaulting logger level to INFO.")
    LOG_LEVEL = logging.INFO

def setup_logger(name="Crawl4AI", level=None):
    """
    Sets up and returns a structured logger with console and file output.

    This function configures a logger that outputs to the console and a
    timestamped log file in the 'logs' directory. The format includes
    timestamp, level, module, and message.
    It ensures that handlers are not added multiple times if called repeatedly.

    Args:
        name (str): The name of the logger.
        level (int): The logging level (e.g., logging.INFO, logging.DEBUG).

    Returns:
        logging.Logger: The configured logger instance.
    """
    if level is None:
        level = LOG_LEVEL

    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Prevent adding multiple handlers to the same logger instance.
    if not logger.handlers:
        # --- Formatter ---
        formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - [%(module)s] - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # --- Console Handler ---
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # --- File Handler ---
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file_path = os.path.join(log_dir, f"agent_{timestamp}.log")

        file_handler = logging.FileHandler(log_file_path, encoding="utf-8")
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


# Create a single logger instance to be used across the application.
logger = setup_logger()
