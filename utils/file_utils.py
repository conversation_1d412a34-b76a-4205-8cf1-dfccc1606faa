import json
import os
import re
from datetime import datetime
from urllib.parse import urlparse


def save_json_to_dir(data: dict, url: str, base_dir: str = "results") -> str:
    """
    Saves a dictionary as a JSON file, organized by domain and timestamp.

    Args:
        data (dict): The dictionary data to save.
        url (str): The URL the data was extracted from, used for naming.
        base_dir (str): The root directory to save results in.

    Returns:
        str: The full path to the saved file.
    """
    domain = urlparse(url).netloc.replace("www.", "")
    domain_dir = os.path.join(base_dir, domain)
    os.makedirs(domain_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(domain_dir, f"{timestamp}.json")

    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=4, ensure_ascii=False)

    return file_path


def save_summary_to_dir(summary_content: str, url: str, base_dir: str = "results") -> str:
    """
    Saves a summary string as a Markdown file, organized by domain and timestamp.

    Args:
        summary_content (str): The summary text to save.
        url (str): The URL the summary is for, used for naming.
        base_dir (str): The root directory to save results in.

    Returns:
        str: The full path to the saved file.
    """
    domain = urlparse(url).netloc.replace("www.", "")
    domain_dir = os.path.join(base_dir, domain)
    os.makedirs(domain_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(domain_dir, f"summary_{timestamp}.md")

    with open(file_path, "w", encoding="utf-8") as f:
        f.write(summary_content)

    return file_path


def save_html_to_dir(html_content: str, url: str, base_dir: str = "scraped_html") -> str:
    """
    Saves raw HTML content to a file, organized by domain and timestamp.

    Args:
        html_content (str): The raw HTML string to save.
        url (str): The URL the HTML was fetched from, used for naming.
        base_dir (str): The root directory to save HTML files in.

    Returns:
        str: The full path to the saved HTML file.
    """
    domain = urlparse(url).netloc.replace("www.", "")
    domain_dir = os.path.join(base_dir, domain)
    os.makedirs(domain_dir, exist_ok=True)

    # --- Generate a descriptive page name from the URL path ---
    path = urlparse(url).path
    # Get the last segment of the path, e.g., 'contact' from '/about/contact/'
    page_name = os.path.basename(path.rstrip('/'))
    # If the path was the root, basename is empty, so default to 'index'
    if not page_name:
        page_name = 'index'
    # Remove file extension, e.g., '.html'
    page_name = os.path.splitext(page_name)[0]
    # Sanitize for use in a filename and limit length
    page_name = re.sub(r'[^\w-]', '_', page_name).lower()
    page_name = page_name[:50]

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(domain_dir, f"{page_name}_{timestamp}.html")

    with open(file_path, "w", encoding="utf-8") as f:
        f.write(html_content)

    return file_path


def read_local_file(file_path: str) -> str | None:
    """
    Reads a local file and returns its content after ensuring the path is absolute.

    Args:
        file_path (str): The path (relative or absolute) to the local file.

    Returns:
        The content of the file as a string, or None if the file cannot be found or read.
    """
    abs_path = os.path.abspath(file_path)
    if not os.path.exists(abs_path):
        # The calling function will log a more specific error.
        print(f"Error: File does not exist at the resolved path: {abs_path}")
        return None
    try:
        with open(abs_path, "r", encoding="utf-8") as f:
            return f.read()
    except IOError as e:
        print(f"Error: Could not read file {abs_path}: {e}")
        return None


def save_media_to_dir(media_content: bytes, url: str, media_type: str = "screenshots", extension: str = "png") -> str:
    """
    Saves binary media content (screenshots, PDFs) to a file, organized by domain and timestamp.

    Args:
        media_content (bytes): The binary content to save
        url (str): The URL the media was captured from, used for naming
        media_type (str): Type of media (e.g., "screenshots", "pdfs")
        extension (str): File extension without dot (e.g., "png", "pdf")

    Returns:
        str: The full path to the saved file
    """
    domain = urlparse(url).netloc.replace("www.", "")
    domain_dir = os.path.join(media_type, domain)
    os.makedirs(domain_dir, exist_ok=True)

    # --- Generate a descriptive page name from the URL path ---
    path = urlparse(url).path
    page_name = os.path.basename(path.rstrip('/'))
    if not page_name:
        page_name = 'index'
    page_name = os.path.splitext(page_name)[0]
    page_name = re.sub(r'[^\w-]', '_', page_name).lower()
    page_name = page_name[:50]

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(domain_dir, f"{page_name}_{timestamp}.{extension}")

    with open(file_path, "wb") as f:
        f.write(media_content)

    return file_path
