import json
import re


def extract_json_from_response(response_text: str) -> dict:
    """
    Extracts a JSON object from a string that might contain other text,
    such as markdown code blocks.

    Args:
        response_text (str): The string containing the JSON object.

    Returns:
        dict: The extracted JSON object, or an empty dictionary if not found.
    """
    # First, try to find JSON wrapped in markdown code blocks
    json_block_pattern = re.search(r"```(?:json)?\s*(\{.*?\})\s*```", response_text, re.DOTALL)
    if json_block_pattern:
        json_str = json_block_pattern.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # If parsing fails, continue to the next method
            pass

    # Next, try to find a standalone JSON object
    json_object_pattern = re.search(r"(\{[^{]*?\"company_name\".*?\})", response_text, re.DOTALL)
    if json_object_pattern:
        json_str = json_object_pattern.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # If parsing fails, try to clean the string
            cleaned_json = clean_json_string(json_str)
            try:
                return json.loads(cleaned_json)
            except json.JSONDecodeError:
                # If cleaning doesn't help, continue to the next method
                pass

    # As a last resort, try to find any JSON-like object
    any_json_pattern = re.search(r"(\{.*?\})", response_text, re.DOTALL)
    if any_json_pattern:
        json_str = any_json_pattern.group(1)
        try:
            return json.loads(json_str)
        except json.JSONDecodeError:
            # Try with cleaning
            cleaned_json = clean_json_string(json_str)
            try:
                return json.loads(cleaned_json)
            except json.JSONDecodeError:
                # If all attempts fail, return an empty dict
                return {}
    
    return {}


def clean_json_string(json_str: str) -> str:
    """
    Cleans a JSON string to make it more likely to parse correctly.
    
    Args:
        json_str (str): The potentially malformed JSON string.
        
    Returns:
        str: A cleaned JSON string.
    """
    # Replace single quotes with double quotes
    cleaned = re.sub(r"'([^']*)':", r'"\1":', json_str)
    cleaned = re.sub(r":'([^']*)'", r':"\1"', cleaned)
    
    # Fix trailing commas before closing brackets
    cleaned = re.sub(r",\s*}", "}", cleaned)
    cleaned = re.sub(r",\s*]", "]", cleaned)
    
    # Fix missing quotes around keys
    cleaned = re.sub(r"([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', cleaned)
    
    # Fix unescaped quotes within strings
    cleaned = re.sub(r'(?<!\\)"([^"]*?)(?<!\\)"([^"]*?)(?<!\\)"', r'"\1\"\2"', cleaned)
    
    # Fix missing commas between key-value pairs
    cleaned = re.sub(r"}\s*{", "},{", cleaned)
    
    return cleaned
