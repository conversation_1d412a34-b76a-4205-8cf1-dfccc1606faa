"""
Crawl4AI Agent - Main Entry Point

This is the unified main entry point that supports both:
1. Traditional command-based interface (chat_loop)
2. LangGraph-based natural language interface (langgraph_terminal)

The LangGraph interface is the recommended approach for its advanced
natural language understanding and agentic architecture.
"""

import asyncio
import sys
from agent.chat_loop import chat_loop
from agent.langgraph_terminal import main as langgraph_main


def show_interface_selection():
    """Display interface selection menu."""
    print("🔍 Crawl4AI Agent - Interface Selection")
    print("=" * 50)
    print("Choose your preferred interface:")
    print()
    print("1. 🚀 LangGraph Agent (Recommended)")
    print("   • Natural language understanding")
    print("   • Advanced agentic architecture")
    print("   • Schema learning capabilities")
    print("   • Intelligent workflow orchestration")
    print()
    print("2. 💬 Traditional Chat Interface")
    print("   • Simple command-based interaction")
    print("   • Direct function calls")
    print("   • Legacy compatibility")
    print()
    print("3. ❓ Help - Learn more about the interfaces")
    print()


def show_help():
    """Display detailed help information."""
    print("🔍 Crawl4AI Agent - Interface Help")
    print("=" * 50)
    print()
    print("🚀 LangGraph Agent (Option 1) - RECOMMENDED")
    print("─" * 40)
    print("The LangGraph interface uses advanced AI to understand natural language")
    print("commands and automatically route them through the appropriate workflow.")
    print()
    print("Features:")
    print("• Natural language processing with intent recognition")
    print("• Automatic parameter extraction from conversational input")
    print("• Schema learning for structured data extraction")
    print("• Intelligent error handling and recovery")
    print("• Support for complex multi-step operations")
    print()
    print("Example commands:")
    print("• 'crawl imprint from example.com'")
    print("• 'smart crawl https://shop.com for product data'")
    print("• 'extract sitemap from docs.python.org'")
    print("• 'search and crawl best python tutorials'")
    print("• 'summarize this website with screenshots'")
    print()
    print("💬 Traditional Chat Interface (Option 2)")
    print("─" * 40)
    print("The traditional interface uses simple command parsing with direct")
    print("function calls. It's more predictable but less flexible.")
    print()
    print("Features:")
    print("• Simple command-based interaction")
    print("• Direct function calls without AI interpretation")
    print("• Faster for users who know exact commands")
    print("• Legacy compatibility with older scripts")
    print()
    print("Example commands:")
    print("• 'summarize example.com'")
    print("• 'impressum example.com --screenshot'")
    print("• 'search python tutorials'")
    print()


async def main():
    """
    Main entry point with interface selection.

    Supports command line arguments:
    --langgraph or -l: Start LangGraph interface directly
    --traditional or -t: Start traditional interface directly
    --help or -h: Show help and exit
    """
    # Check for command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--langgraph', '-l']:
            print("🚀 Starting LangGraph Agent...")
            await langgraph_main()
            return
        elif arg in ['--traditional', '-t']:
            print("💬 Starting Traditional Chat Interface...")
            await chat_loop()
            return
        elif arg in ['--help', '-h']:
            show_help()
            return
        else:
            print(f"❌ Unknown argument: {sys.argv[1]}")
            print("Use --help to see available options")
            return

    # Interactive interface selection
    while True:
        show_interface_selection()
        try:
            choice = input("Enter your choice (1-3): ").strip()

            if choice == '1':
                print("🚀 Starting LangGraph Agent...")
                print()
                await langgraph_main()
                break
            elif choice == '2':
                print("💬 Starting Traditional Chat Interface...")
                print()
                await chat_loop()
                break
            elif choice == '3':
                show_help()
                input("\nPress Enter to return to the menu...")
                print()
                continue
            else:
                print("❌ Invalid choice. Please enter 1, 2, or 3.")
                print()
                continue

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ An error occurred: {e}")
            print("Please try again.")


if __name__ == "__main__":
    asyncio.run(main())
