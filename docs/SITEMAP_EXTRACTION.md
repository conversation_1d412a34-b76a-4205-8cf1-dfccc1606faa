# Sitemap Extraction

The sitemap extraction feature allows you to automatically discover and extract all crawlable URLs from website sitemaps. This is useful for comprehensive website crawling, SEO analysis, and content discovery.

## Features

- **Automatic Discovery**: Tries standard sitemap location (`/sitemap.xml`) first, then falls back to `robots.txt`
- **Multiple Formats**: Supports both regular sitemaps and sitemap indexes
- **Compression Support**: Handles both `.xml` and `.xml.gz` formats
- **Recursive Processing**: Automatically follows and processes nested sitemaps from sitemap indexes
- **Error Handling**: Graceful handling of timeouts, 404s, and malformed XML
- **Batch Processing**: Process multiple websites concurrently
- **Deduplication**: Returns unique URLs only

## Quick Start

### Single Sitemap Extraction

```python
import asyncio
from crawler.sitemap import read_sitemap

async def extract_sitemap():
    result = await read_sitemap('https://docs.python.org')
    
    print(f"Sitemap source: {result['source']}")
    print(f"URLs found: {len(result['urls'])}")
    
    # Display first few URLs
    for url in result['urls'][:5]:
        print(f"  - {url}")

# Run the extraction
asyncio.run(extract_sitemap())
```

### Batch Processing

```python
import asyncio
from crawler.sitemap import read_multiple_sitemaps

async def batch_extract():
    websites = [
        'https://docs.python.org',
        'https://www.djangoproject.com',
        'https://flask.palletsprojects.com'
    ]
    
    results = await read_multiple_sitemaps(websites)
    
    for result in results:
        if result['success']:
            print(f"✅ {result['base_url']}: {len(result['urls'])} URLs")
        else:
            print(f"❌ {result['base_url']}: {result['error']}")

asyncio.run(batch_extract())
```

## API Reference

### `read_sitemap(url, timeout=30)`

Extract URLs from a single website's sitemap.

**Parameters:**
- `url` (str): The base URL of the website (e.g., "https://example.com")
- `timeout` (int): Request timeout in seconds (default: 30)

**Returns:**
```python
{
    "source": "https://example.com/sitemap.xml",  # or None if not found
    "urls": ["https://example.com/page1", ...]    # List of extracted URLs
}
```

**Example:**
```python
result = await read_sitemap('https://example.com', timeout=15)
```

### `read_multiple_sitemaps(urls, timeout=30)`

Extract URLs from multiple websites concurrently.

**Parameters:**
- `urls` (List[str]): List of base URLs to process
- `timeout` (int): Request timeout in seconds (default: 30)

**Returns:**
```python
[
    {
        "base_url": "https://example.com",
        "source": "https://example.com/sitemap.xml",
        "urls": ["https://example.com/page1", ...],
        "success": True,
        "error": None
    },
    # ... more results
]
```

**Example:**
```python
websites = ['https://site1.com', 'https://site2.com']
results = await read_multiple_sitemaps(websites, timeout=20)
```

## How It Works

### Discovery Process

1. **Standard Location**: First tries `https://domain/sitemap.xml`
2. **Robots.txt Fallback**: If not found, parses `https://domain/robots.txt` for `Sitemap:` entries
3. **Processing**: Downloads and parses each discovered sitemap

### Sitemap Types

#### Regular Sitemap
Contains actual page URLs:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://example.com/page1</loc>
        <lastmod>2023-01-01</lastmod>
    </url>
    <url>
        <loc>https://example.com/page2</loc>
        <lastmod>2023-01-02</lastmod>
    </url>
</urlset>
```

#### Sitemap Index
Contains references to other sitemaps:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <sitemap>
        <loc>https://example.com/sitemap1.xml</loc>
        <lastmod>2023-01-01</lastmod>
    </sitemap>
    <sitemap>
        <loc>https://example.com/sitemap2.xml</loc>
        <lastmod>2023-01-02</lastmod>
    </sitemap>
</sitemapindex>
```

### Error Handling

The system gracefully handles various error conditions:

- **Network Errors**: Timeouts, DNS failures, connection errors
- **HTTP Errors**: 404, 500, and other HTTP status codes
- **Format Errors**: Invalid XML, malformed sitemaps
- **Compression Errors**: Issues with gzipped content

Failed sitemaps are logged and skipped, allowing processing to continue with remaining entries.

## Integration Examples

### With Core Crawler

```python
from crawler.sitemap import read_sitemap
from crawler.core import crawl_multiple_websites

async def crawl_from_sitemap():
    # Extract URLs from sitemap
    sitemap_result = await read_sitemap('https://example.com')
    
    if sitemap_result['urls']:
        # Crawl the discovered URLs
        results = await crawl_multiple_websites(
            sitemap_result['urls'][:10],  # Limit to first 10 URLs
            screenshot=False,
            pdf=False,
            extract_links=True
        )
        
        return results
```

### Saving Results

```python
import json
from datetime import datetime

async def save_sitemap_results():
    result = await read_sitemap('https://example.com')
    
    if result['urls']:
        # Prepare data for saving
        save_data = {
            'extraction_info': {
                'base_url': 'https://example.com',
                'sitemap_source': result['source'],
                'extracted_at': datetime.now().isoformat(),
                'total_urls': len(result['urls'])
            },
            'urls': result['urls']
        }
        
        # Save to file
        with open('sitemap_results.json', 'w') as f:
            json.dump(save_data, f, indent=2)
```

## Best Practices

1. **Respect Rate Limits**: Use appropriate timeouts and don't overwhelm servers
2. **Handle Large Sitemaps**: Some sitemaps can contain thousands of URLs
3. **Filter URLs**: Consider filtering URLs based on your crawling needs
4. **Cache Results**: Sitemaps don't change frequently, consider caching
5. **Monitor Errors**: Log and monitor failed extractions for debugging

## Troubleshooting

### Common Issues

**No sitemap found:**
- Website might not have a sitemap
- Sitemap might be in a non-standard location
- Check robots.txt manually for sitemap entries

**Empty results:**
- Sitemap might be malformed
- Network issues preventing download
- Check logs for specific error messages

**Timeout errors:**
- Increase timeout parameter
- Check network connectivity
- Some sitemaps might be very large

### Debugging

Enable debug logging to see detailed processing information:

```python
import logging
logging.getLogger('Crawl4AI').setLevel(logging.DEBUG)
```

This will show:
- Sitemap discovery attempts
- Download progress
- XML parsing details
- Error details
