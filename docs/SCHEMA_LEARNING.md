# Schema Learning Crawler

The Schema Learning Crawler is an advanced feature that automatically learns CSS selector patterns for structured data extraction from websites. It uses Large Language Models (LLMs) to analyze webpage structure and create reusable extraction schemas.

## Overview

Traditional web scraping requires manual analysis of HTML structure and writing CSS selectors. The Schema Learning Crawler automates this process by:

1. **Learning Phase**: Uses an LLM to analyze the webpage and generate CSS selectors based on your target data model
2. **Caching Phase**: Saves the learned schema for future use
3. **Extraction Phase**: Uses the cached schema for fast, consistent data extraction

## Key Features

- **Automatic Schema Generation**: LLM analyzes webpage structure and creates CSS selectors
- **Schema Caching**: Learned schemas are saved and reused for better performance
- **Pydantic Integration**: Define your data structure using Pydantic models
- **Multiple LLM Providers**: Support for various LLM providers (Ollama, OpenAI, etc.)
- **Flexible Configuration**: Customizable schema directories and learning parameters

## Basic Usage

### 1. Define Your Data Model

```python
from pydantic import BaseModel, Field

class Product(BaseModel):
    name: str = Field(..., description="Product name")
    price: str = Field(..., description="Product price")
    description: str = Field(..., description="Product description")
    availability: str = Field(..., description="Availability status")
```

### 2. Use the Smart Crawler

```python
import asyncio
from crawler.crawler import smart_crawler

async def extract_products():
    products = await smart_crawler(
        url="https://example-shop.com/products",
        target_model=Product,
        provider="ollama/qwen3"
    )
    
    for product in products:
        print(f"Product: {product.name} - {product.price}")

asyncio.run(extract_products())
```

## Advanced Configuration

### Custom Schema Directory

```python
products = await smart_crawler(
    url="https://example.com",
    target_model=Product,
    schema_dir="./my_custom_schemas"
)
```

### Force Schema Learning

```python
# Force learning a new schema even if one exists
products = await smart_crawler(
    url="https://example.com",
    target_model=Product,
    force_learn=True
)
```

### Different LLM Providers

```python
# Using OpenAI
products = await smart_crawler(
    url="https://example.com",
    target_model=Product,
    provider="openai/gpt-4",
    api_token="your-openai-api-key"
)

# Using Anthropic Claude
products = await smart_crawler(
    url="https://example.com",
    target_model=Product,
    provider="anthropic/claude-3",
    api_token="your-anthropic-api-key"
)
```

## Schema Management

### Manual Schema Management

```python
from crawler.crawler import SchemaLearner

# Create schema learner
learner = SchemaLearner(schema_dir="./schemas")

# Load existing schema
schema = learner.load_schema("https://example.com")

# Save custom schema
custom_schema = {
    "name": "product_extractor",
    "baseSelector": ".product-item",
    "fields": [
        {"name": "name", "selector": ".product-name", "type": "text"},
        {"name": "price", "selector": ".price", "type": "text"}
    ]
}
learner.save_schema("https://example.com", custom_schema)
```

### Schema File Structure

Learned schemas are stored as JSON files in the specified directory:

```
learned_schemas/
├── example.com_schema.json
├── shop.example.com_schema.json
└── news.example.com_schema.json
```

Each schema file contains:

```json
{
  "name": "product_extractor",
  "baseSelector": ".product-item",
  "fields": [
    {
      "name": "name",
      "selector": ".product-name",
      "type": "text"
    },
    {
      "name": "price",
      "selector": ".price",
      "type": "text"
    }
  ]
}
```

## Integration with LangGraph Agent

The schema learning crawler is integrated into the LangGraph workflow and can be used through natural language commands:

```python
from agent.langgraph_workflow import create_workflow

# Create workflow
workflow = create_workflow()

# Use through natural language
result = await workflow.ainvoke({
    "user_prompt": "smart crawl https://example.com for product data",
    "target_model": Product,
    "schema_provider": "ollama/qwen3"
})
```

## Best Practices

### 1. Start Simple
Begin with simple data models and gradually add complexity:

```python
# Start with basic fields
class SimpleProduct(BaseModel):
    name: str
    price: str

# Expand as needed
class DetailedProduct(BaseModel):
    name: str = Field(..., description="Product name")
    price: str = Field(..., description="Product price")
    description: str = Field(..., description="Product description")
    images: List[str] = Field(..., description="Product image URLs")
    specifications: Dict[str, str] = Field(..., description="Product specs")
```

### 2. Use Descriptive Field Descriptions
Clear descriptions help the LLM understand what to extract:

```python
class Article(BaseModel):
    title: str = Field(..., description="Main article headline")
    author: str = Field(..., description="Article author name")
    publish_date: str = Field(..., description="Publication date in any format")
    content: str = Field(..., description="Full article text content")
```

### 3. Test with Different Websites
Different websites may require different schemas even for similar data:

```python
# Use separate schema directories for different sites
ecommerce_products = await smart_crawler(
    url="https://shop1.com/products",
    target_model=Product,
    schema_dir="./schemas/shop1"
)

marketplace_products = await smart_crawler(
    url="https://marketplace.com/items",
    target_model=Product,
    schema_dir="./schemas/marketplace"
)
```

### 4. Monitor Performance
- First run (learning): Slower due to LLM analysis
- Subsequent runs (cached): Much faster using saved schema
- Consider force learning if website structure changes

## Troubleshooting

### Common Issues

1. **Schema Learning Fails**
   - Check LLM provider configuration
   - Ensure target model is well-defined
   - Try with simpler data models first

2. **Poor Extraction Quality**
   - Use `force_learn=True` to regenerate schema
   - Improve field descriptions in your Pydantic model
   - Try different LLM providers

3. **No Data Extracted**
   - Verify the website structure matches your model
   - Check if the website uses dynamic content loading
   - Inspect the learned schema file for accuracy

### Error Handling

```python
try:
    products = await smart_crawler(
        url="https://example.com",
        target_model=Product
    )
except Exception as e:
    print(f"Schema learning failed: {e}")
    # Fallback to traditional crawling or manual schema
```

## Performance Considerations

- **First Run**: Slower due to LLM analysis (30-60 seconds)
- **Cached Runs**: Fast extraction using saved schema (2-5 seconds)
- **Schema Size**: Larger schemas may take longer to learn
- **LLM Provider**: Different providers have different speeds and costs

## Limitations

- Requires LLM provider access (local or API)
- Learning phase can be slow for complex websites
- May need manual schema adjustment for highly dynamic sites
- Best suited for structured, consistent webpage layouts

## Future Enhancements

- Support for dynamic content (JavaScript-rendered pages)
- Schema validation and auto-correction
- Multi-page schema learning
- Schema sharing and community schemas
- Performance optimization for large-scale extraction
