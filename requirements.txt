aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
beautifulsoup4==4.13.4
black==25.1.0
Brotli==1.1.0
certifi==2025.6.15
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
Crawl4AI==0.6.3
cryptography==45.0.4
cssselect==1.3.0
distro==1.9.0
fake-http-header==0.3.5
fake-useragent==2.2.0
filelock==3.18.0
frozenlist==1.7.0
fsspec==2025.5.1
greenlet==3.2.3
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.33.0
humanize==4.12.3
idna==3.10
importlib_metadata==8.7.0
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
litellm==1.73.0
lxml==5.4.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
multidict==6.5.0
mypy_extensions==1.1.0
nltk==3.9.1
numpy==2.3.1
openai==1.90.0
packaging==25.0
pathspec==0.12.1
pillow==10.4.0
platformdirs==4.3.8
playwright==1.52.0
propcache==0.3.2
psutil==7.0.0
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pyee==13.0.0
Pygments==2.19.2
pyOpenSSL==25.1.0
pyperclip==1.9.0
python-dotenv==1.1.0
PyYAML==6.0.2
rank-bm25==0.2.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rich==14.0.0
rpds-py==0.25.1
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.7
tf-playwright-stealth==1.2.0
tiktoken==0.9.0
tokenizers==0.21.1
tqdm==4.67.1
typing-inspection==0.4.1
typing_extensions==4.14.0
urllib3==2.5.0
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0

# LangGraph and LangChain dependencies for agentic system
langgraph>=0.5.1
langchain>=0.3.26
langchain-core>=0.3.66
langchain-community>=0.3.26
langchain-ollama>=0.3.3

# Search engine integration
ddgs>=1.0.0

# Testing dependencies
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
