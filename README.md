# Website Crawler & Company Data Extractor

This project provides a Python-based, command-line agent that can:

- Crawl website content using [crawl4ai](https://docs.crawl4ai.com/)
- **Extract and categorize links** with titles from web pages (internal vs external)
- **Sitemap extraction** - automatically discover and extract all URLs from website sitemaps
- **Batch processing** of multiple URLs for efficient bulk operations
- **Schema Learning Crawler** with automatic CSS selector generation using LLMs
- **Crawl Statistics Tracking** - comprehensive analytics with historical data import and performance monitoring
- Summarize content using a local Ollama LLM (e.g., `qwen3`, `llama3`).
- Extract structured company details from "Impressum" or "Legal Notice" pages.
- Save extracted data as JSON files, organized by domain and timestamp

---

## Features

- **Natural Language CLI:** Interact with the agent using natural language commands (e.g., "summarize example.com").
- **Schema Learning Crawler:** Automatically learns CSS selectors for structured data extraction using LLMs.
- **Sitemap Extraction:** Automatically discover and extract all crawlable URLs from website sitemaps (XML and XML.gz).
- **Link Extraction:** Extract and categorize internal/external links with titles and anchor text.
- **Batch Processing:** Process multiple URLs simultaneously with comma-separated lists.
- **Async Performance:** Fast crawling and LLM interaction via Python async/await.
- **Structured Storage:** Results saved in `/results` directory, auto-named by website and time.
- **Robust JSON Extraction:** Handles LLM "hallucinations" and common output quirks.
- **Local File Processing:** Can read and process local HTML files.
- **Crawl Statistics:** Comprehensive tracking and analytics for all crawl operations with historical data import, domain analysis, and performance insights.
- **Changelog:** All changes are documented in the CHANGELOG.md.
- **Multi-Language Support:** Automatically adapts to German and English Impressum/Legal Notice pages based on TLD.

---

## Setup

### 1. Clone this repository
```bash
git clone <your-repo-url>
cd <your-repo-folder>
```

### 2. Create and activate a virtual environment (optional but recommended)
```bash
python -m venv .venv
# On macOS/Linux:
source .venv/bin/activate
# On Windows:
.venv\Scripts\activate
```

### 3. Install dependencies
```bash
pip install -r requirements.txt
```

### 4. Set up Ollama

- [Install Ollama](https://ollama.com/download) and ensure it's running locally (`http://localhost:11434`)
- Pull your desired model (e.g., `gemma3`, `qwen3`):
  ```bash
      ollama pull qwen3
  ```

---

## Usage

### Quick Start

```bash
python main.py
```

This will show you an interface selection menu:

```
🔍 Crawl4AI Agent - Interface Selection
==================================================
Choose your preferred interface:

1. 🚀 LangGraph Agent (Recommended)
   • Natural language understanding
   • Advanced agentic architecture
   • Schema learning capabilities
   • Intelligent workflow orchestration

2. 💬 Traditional Chat Interface
   • Simple command-based interaction
   • Direct function calls
   • Legacy compatibility

3. ❓ Help - Learn more about the interfaces
```

### Direct Interface Launch

You can also start a specific interface directly:

```bash
# Start LangGraph Agent directly (recommended)
python main.py --langgraph

# Start Traditional Interface directly
python main.py --traditional

# Show detailed help
python main.py --help
```

### LangGraph Agent Examples (Recommended)

The LangGraph interface understands natural language:

```bash
python main.py --langgraph
🚀 Crawl4AI LangGraph Agent - Natural Language Interface
🧠 Your instruction: summarize example.com
```

```bash
🧠 Your instruction: smart crawl https://shop.com for product data
```

```bash
🧠 Your instruction: search and crawl best python tutorials
```

### Traditional Interface Examples

The traditional interface uses simple commands:

```bash
python main.py --traditional
🔍 Crawl4AI Agent: Ready to receive instructions.
🧠 Your instruction: summarize example.com
```

### Extract links from a website

**LangGraph Agent:**
```bash
🧠 Your instruction: extract links from example.com
```

**Traditional Interface:**
```bash
🧠 Your instruction: extract links from example.com
```

The tool will:
1. Crawl the website and extract all links
2. Categorize links as internal (same domain) or external (different domains)
3. Include link titles and anchor text
4. Save structured data to JSON file with page title and metadata

**Supported link extraction commands:**
- `extract links from example.com`
- `get links from example.com`
- `find links from example.com`
- `crawl and extract links from example.com`

### Extract URLs from website sitemaps

**LangGraph Agent:**
```bash
🧠 Your instruction: extract sitemap from docs.python.org
```

**Traditional Interface:**
```bash
🧠 Your instruction: sitemap docs.python.org
```

The tool will:
1. Try standard sitemap location (`/sitemap.xml`) first
2. Fall back to parsing `robots.txt` for sitemap entries
3. Handle both regular sitemaps and sitemap indexes
4. Support compressed (`.xml.gz`) sitemaps
5. Recursively process nested sitemaps
6. Return deduplicated list of all discoverable URLs

**Supported sitemap extraction commands:**
- `extract sitemap from example.com`
- `get sitemap from example.com`
- `read sitemap from example.com`
- `sitemap example.com`

**Programmatic Usage:**
```python
from crawler.sitemap import read_sitemap, read_multiple_sitemaps

# Single sitemap
result = await read_sitemap('https://docs.python.org')
print(f"Found {len(result['urls'])} URLs from {result['source']}")

# Multiple sitemaps
websites = ['https://site1.com', 'https://site2.com']
results = await read_multiple_sitemaps(websites)
```

For detailed documentation, see [SITEMAP_EXTRACTION.md](docs/SITEMAP_EXTRACTION.md).

### Process multiple URLs at once

**LangGraph Agent:**
```bash
🧠 Your instruction: summarize example.com,google.com,python.org
```

**Traditional Interface:**
```bash
🧠 Your instruction: summarize example.com,google.com,python.org
```

The tool will:
1. Process each URL in the comma-separated list
2. Provide individual results for each website
3. Track success/failure status for each URL
4. Save files separately for each domain

**Supported multiple URL commands:**
- `summarize url1,url2,url3` - Batch summarization
- `impressum url1,url2,url3` - Batch impressum extraction
- `extract links from url1,url2,url3` - Batch link extraction
- `crawl url1,url2,url3 --screenshot --pdf` - Batch crawling with options

**URL separators supported:** comma (`,`), semicolon (`;`), pipe (`|`)

### Extract company data from Impressum page

**LangGraph Agent:**
```bash
🧠 Your instruction: crawl imprint from example.com
```

**Traditional Interface:**
```bash
🧠 Your instruction: impressum example.com
```

The tool will:
1. Automatically detect the website's TLD (Top Level Domain)
2. Choose appropriate extraction prompts (German for .de, .at, .ch; English for others)
3. Extract and save the company data in a structured JSON format

Supported TLDs for specialized extraction:
- German: .de, .at, .ch
- English: .com, .co.uk, .uk, .nl, .fr (and others)

your_project/
├── main.py # CLI entry point
├── crawler.py # Crawling logic (website & Impressum)
├── ollama_client.py # Interacts with local Ollama LLM
├── json_utils.py # Utility for robust JSON extraction
├── file_utils.py # Utility for saving JSON files
├── prompts/ # Language-specific extraction prompts
│ ├── __init__.py
│ └── extract_company_data_prompt.py
├── requirements.txt # Dependencies
├── results/ # Output JSON files
└── README.md

## Schema Learning Crawler

The Schema Learning Crawler automatically learns CSS selectors for structured data extraction using LLMs. This feature allows you to extract structured data from websites without manually writing CSS selectors.

### Quick Start

```python
import asyncio
from pydantic import BaseModel, Field
from crawler.crawler import smart_crawler

class Product(BaseModel):
    name: str = Field(..., description="Product name")
    price: str = Field(..., description="Product price")
    description: str = Field(..., description="Product description")

async def extract_products():
    products = await smart_crawler(
        url="https://example-shop.com/products",
        target_model=Product,
        provider="ollama/qwen3"
    )

    for product in products:
        print(f"Product: {product.name} - {product.price}")

asyncio.run(extract_products())
```

### Key Features

- **Automatic Schema Generation**: LLM analyzes webpage structure and creates CSS selectors
- **Schema Caching**: Learned schemas are saved and reused for better performance
- **Pydantic Integration**: Define your data structure using Pydantic models
- **Multiple LLM Providers**: Support for Ollama, OpenAI, Anthropic, and more

For detailed documentation, see [docs/SCHEMA_LEARNING.md](docs/SCHEMA_LEARNING.md) and [examples/schema_learning_examples.py](examples/schema_learning_examples.py).

## Crawl Statistics & Analytics

The agent automatically tracks all crawl operations and provides comprehensive analytics with **full historical data import** capabilities:

### 🚀 Quick Start

**View Statistics:**
```bash
🧠 Your instruction: show crawl statistics
🧠 Your instruction: analyze domain example.com
🧠 Your instruction: show recent activity
```

**Import Historical Data:**
```bash
# Preview what data would be imported
python import_historical_data.py --preview

# Import historical crawl data
python import_historical_data.py --import
```

### 📊 Real-World Performance

After importing historical data, you'll see comprehensive analytics:
- **📈 555+ crawl records** across 55 unique domains
- **✅ 99.1% success rate** with excellent reliability
- **⚡ 3.61 seconds** average crawl duration
- **💾 ~1GB** total content successfully crawled

### 🔧 Programmatic Access

```python
from crawler.statistics import StatisticsAnalyzer, StatisticsDatabase

# Generate comprehensive report
analyzer = StatisticsAnalyzer()
report = analyzer.generate_comprehensive_report(days_back=30)

print(f"Total crawls: {report.summary_stats['total_crawls']}")
print(f"Success rate: {report.summary_stats['success_rate']:.1f}%")
print(f"Top domains: {[d['domain'] for d in report.top_domains[:5]]}")

# Database operations
db = StatisticsDatabase()
domains = db.get_top_domains(10)
recent_activity = db.get_recent_crawl_activity(limit=20)

# Rebuild statistics if needed
db.rebuild_statistics_tables()
```

### ✨ Key Features

- **🔄 Historical Import**: Import existing crawl data from files and logs with intelligent pattern recognition
- **📈 Domain Analytics**: Success rates, crawl frequency, performance metrics per domain
- **⏱️ Time-based Analysis**: Configurable granularity (hourly, daily, weekly, monthly)
- **🎯 Performance Insights**: Duration tracking, content size monitoring, response codes
- **🛡️ Error Categorization**: Detailed error analysis with common failure patterns
- **🔧 Database Maintenance**: Automatic repair and rebuilding of statistics tables
- **⚡ High Performance**: Optimized with SQLite WAL mode, caching, and connection pooling

### 🧪 Demo & Testing

```bash
# Interactive statistics demo
python examples/statistics_demo.py

# Run comprehensive tests
python -m pytest tests/crawler/statistics/ -v
```

### 🔧 Troubleshooting

If statistics show "No result data available":
1. **Import historical data**: `python import_historical_data.py --import`
2. **Rebuild statistics**: Use the `rebuild_statistics_tables()` method
3. **Check database**: Verify `stats/crawl_statistics.db` exists and has data

For detailed documentation, see [docs/STATISTICS_TRACKING.md](docs/STATISTICS_TRACKING.md).

## Extending

- **Add new LLM models:** Change the model in . `ollama_client.py`
- **Support more languages:** Add new prompt templates in . `prompts/extract_company_data_prompt.py`
- **Batch processing:** Loop over URLs in . `main.py`
- **Custom output format:** Edit or add to . `file_utils.py`
- **Custom Schema Learning:** Extend the `SchemaLearner` class for specialized extraction patterns

## Troubleshooting

- If you get a JSON parsing error, check that the model output is well-formed. The tool auto-cleans most common cases.
- Ensure your Ollama instance is running and has the required model pulled.
- For TLD-specific issues, check the mapping in . `prompts/extract_company_data_prompt.py`

## License

[MIT](LICENSE) or [Apache 2.0](https://www.apache.org/licenses/LICENSE-2.0) (adapt as needed)

## Credits

- [crawl4ai](https://docs.crawl4ai.com/) for robust crawling
- [Ollama](https://ollama.com/) for LLM summarization/extraction

_Feel free to contribute or suggest improvements!_
