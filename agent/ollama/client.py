"""
Backward compatibility client for the refactored Ollama services.
Maintains the original function signatures while using the new modular architecture.
"""

from typing import Dict, Any
from .factory import get_default_factory
from .exceptions import OllamaError
from utils.logger import logger


async def get_summary_from_ollama(content: str, model: str = None) -> str:
    """
    Backward compatibility function for generating summaries.
    
    Args:
        content: The text content to summarize
        model: Optional model override
        
    Returns:
        The generated summary or error message
    """
    try:
        factory = get_default_factory()
        summary_service = factory.get_summary_service()
        return await summary_service.generate_summary(content, model)
    except OllamaError as e:
        logger.error(f"Summary generation failed: {e}")
        return f"Error communicating with Ollama for summary: {e}"
    except Exception as e:
        logger.error(f"Unexpected error in summary generation: {e}")
        return f"An unexpected error occurred during summarization: {e}"


async def get_chat_response_from_ollama(prompt: str, model: str = None) -> str:
    """
    Backward compatibility function for chat responses.
    
    Args:
        prompt: The user's input/prompt
        model: Optional model override
        
    Returns:
        The generated chat response or error message
    """
    try:
        factory = get_default_factory()
        chat_service = factory.get_chat_service()
        return await chat_service.get_chat_response(prompt, model)
    except OllamaError as e:
        logger.error(f"Chat response generation failed: {e}")
        return f"Error communicating with Ollama: {e}. Is Ollama running?"
    except Exception as e:
        logger.error(f"Unexpected error in chat response: {e}")
        return f"An unexpected error occurred: {str(e)}"


async def parse_command_from_prompt(user_prompt: str, model: str = None) -> Dict[str, Any]:
    """
    Backward compatibility function for command parsing.
    
    Args:
        user_prompt: The raw input from the user
        model: Optional model override
        
    Returns:
        Dictionary containing the parsed "command" and "url"
        Falls back to a 'chat' command on failure
    """
    try:
        factory = get_default_factory()
        parsing_service = factory.get_command_parsing_service()
        return await parsing_service.parse_command(user_prompt, model)
    except Exception as e:
        logger.error(f"Command parsing failed: {e}")
        # Always return fallback for command parsing to maintain compatibility
        return {"command": "chat", "url": None}


async def extract_company_data_from_impressum(
    content: str, 
    url: str, 
    model: str = None
) -> Dict[str, Any]:
    """
    Backward compatibility function for company data extraction.
    
    Args:
        content: The text content of the Impressum page
        url: The URL of the website for context
        model: Optional model override
        
    Returns:
        Dictionary containing extracted company data or error information
    """
    try:
        factory = get_default_factory()
        extraction_service = factory.get_company_data_service()
        return await extraction_service.extract_company_data(content, url, model)
    except OllamaError as e:
        logger.error(f"Company data extraction failed: {e}")
        return {
            "error": f"Error connecting to Ollama: {str(e)}",
            "raw_error": str(e),
        }
    except Exception as e:
        logger.error(f"Unexpected error in company data extraction: {e}")
        return {
            "error": f"An unexpected error occurred: {str(e)}",
            "raw_error": str(e),
        }
