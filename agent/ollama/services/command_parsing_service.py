"""
Command parsing service for interpreting natural language commands using Ollama.
Handles intent recognition and structured command extraction.
"""

from typing import Optional, Dict, Any
from utils.logger import logger
from utils.json_utils import extract_json_from_response
from prompts import build_parse_command_prompt
from ..base_client import BaseOllamaClient
from ..config import OllamaConfig
from ..exceptions import OllamaError, OllamaParsingError


class CommandParsingService(BaseOllamaClient):
    """Service for parsing natural language commands using Ollama."""
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        """Initialize the command parsing service."""
        super().__init__(config)
    
    async def parse_command(
        self,
        user_prompt: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Parse a natural language prompt into a structured command.
        
        Args:
            user_prompt: The raw input from the user
            model: Optional model override
            
        Returns:
            Dictionary containing the parsed "command" and "url"
            Falls back to a 'chat' command on failure
            
        Raises:
            OllamaError: If parsing fails completely
        """
        try:
            # Validate input
            if not user_prompt or not user_prompt.strip():
                return {"command": "chat", "url": None}
            
            # Build the parsing prompt
            prompt = build_parse_command_prompt(user_prompt.strip())
            
            if self.config.log_prompts and self.config.debug_logging:
                logger.debug(f"Command parsing prompt: {prompt[:200]}...")
            
            # Build payload with JSON format
            payload = self._build_payload(
                prompt=prompt,
                model=model,
                temperature=self.config.temperatures.command_parsing,
                format_json=True
            )
            
            # Make the request
            response_data = await self._make_request(
                payload=payload,
                timeout=self.config.timeouts.command_parsing,
                operation_name="command parsing"
            )
            
            # Extract and parse the response
            response_text = response_data.get("response", "")
            
            if not response_text:
                logger.warning("Empty response from command parsing")
                return {"command": "chat", "url": None}
            
            # Parse JSON from response
            parsed_json = extract_json_from_response(response_text)
            
            # Validate the parsed response
            if isinstance(parsed_json, dict) and "command" in parsed_json:
                # Ensure URL key exists for consistency
                if "url" not in parsed_json:
                    parsed_json["url"] = None
                
                logger.info(f"Parsed command: {parsed_json['command']}")
                return parsed_json
            
            # If parsing failed, log and return fallback
            logger.warning(f"Invalid command structure in response: {parsed_json}")
            return {"command": "chat", "url": None}
            
        except OllamaError:
            # Re-raise Ollama-specific errors
            logger.error(f"Ollama error in command parsing: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in command parsing: {e}")
            # Return fallback instead of raising for command parsing
            return {"command": "chat", "url": None}
    
    def validate_parsed_command(self, parsed_command: Dict[str, Any]) -> bool:
        """
        Validate a parsed command structure.
        
        Args:
            parsed_command: The parsed command dictionary
            
        Returns:
            True if valid, False otherwise
        """
        if not isinstance(parsed_command, dict):
            return False
        
        if "command" not in parsed_command:
            return False
        
        command = parsed_command["command"]
        if not isinstance(command, str) or not command.strip():
            return False
        
        # URL can be None or a string
        url = parsed_command.get("url")
        if url is not None and not isinstance(url, str):
            return False
        
        return True
