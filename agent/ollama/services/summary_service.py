"""
Summary service for generating website content summaries using Ollama.
Handles content preprocessing, prompt generation, and response processing.
"""

from typing import Optional
from utils.logger import logger
from ..base_client import BaseOllamaClient
from ..config import OllamaConfig
from ..exceptions import OllamaError


class SummaryService(BaseOllamaClient):
    """Service for generating content summaries using Ollama."""
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        """Initialize the summary service."""
        super().__init__(config)
    
    async def generate_summary(
        self,
        content: str,
        model: Optional[str] = None,
        focus: str = "business activity"
    ) -> str:
        """
        Generate a concise summary of website content.
        
        Args:
            content: The text content to summarize
            model: Optional model override
            focus: The focus area for the summary
            
        Returns:
            The generated summary text
            
        Raises:
            OllamaError: If summary generation fails
        """
        try:
            # Validate input
            if not content or not content.strip():
                return "No content provided for summarization."
            
            # Preprocess content if it's too long
            processed_content = self._preprocess_content(content)
            
            # Build the prompt
            prompt = self._build_summary_prompt(processed_content, focus)
            
            if self.config.log_prompts and self.config.debug_logging:
                logger.debug(f"Summary prompt: {prompt[:200]}...")
            
            # Build payload
            payload = self._build_payload(
                prompt=prompt,
                model=model,
                temperature=self.config.temperatures.summary,
                format_json=False
            )
            
            # Make the request
            response_data = await self._make_request(
                payload=payload,
                timeout=self.config.timeouts.summary,
                operation_name="summary generation"
            )
            
            # Extract and return the summary
            summary = response_data.get("response", "").strip()
            
            if not summary:
                logger.warning("Ollama returned empty summary")
                return "Unable to generate summary - empty response from model."
            
            logger.info(f"Generated summary of {len(summary)} characters")
            return summary
            
        except OllamaError:
            # Re-raise Ollama-specific errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error in summary generation: {e}")
            raise OllamaError(f"Summary generation failed: {str(e)}")
    
    def _preprocess_content(self, content: str) -> str:
        """
        Preprocess content for summarization.
        
        Args:
            content: Raw content to preprocess
            
        Returns:
            Processed content ready for summarization
        """
        # Remove excessive whitespace
        content = ' '.join(content.split())
        
        # Truncate if too long
        if len(content) > self.config.max_content_length:
            logger.info(f"Truncating content from {len(content)} to {self.config.max_content_length} characters")
            content = content[:self.config.max_content_length] + "..."
        
        return content
    
    def _build_summary_prompt(self, content: str, focus: str) -> str:
        """
        Build a prompt for content summarization.
        
        Args:
            content: The content to summarize
            focus: The focus area for the summary
            
        Returns:
            The formatted prompt
        """
        return (
            f"Please give me a concise summary of the following website content "
            f"with focus on {focus}:\n\n"
            f"{content}\n\n"
            f"Summary:"
        )
