"""
Chat service for general conversational interactions using Ollama.
Handles prompt processing and response generation for chat scenarios.
"""

from typing import Optional
from utils.logger import logger
from ..base_client import BaseOllamaClient
from ..config import OllamaConfig
from ..exceptions import OllamaError


class ChatService(BaseOllamaClient):
    """Service for general chat interactions using Ollama."""
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        """Initialize the chat service."""
        super().__init__(config)
    
    async def get_chat_response(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None
    ) -> str:
        """
        Get a chat response from Ollama.
        
        Args:
            prompt: The user's input/prompt
            model: Optional model override
            temperature: Optional temperature override
            
        Returns:
            The generated chat response
            
        Raises:
            OllamaError: If chat response generation fails
        """
        try:
            # Validate input
            if not prompt or not prompt.strip():
                return "Please provide a question or prompt for me to respond to."
            
            # Preprocess prompt
            processed_prompt = self._preprocess_prompt(prompt)
            
            if self.config.log_prompts and self.config.debug_logging:
                logger.debug(f"Chat prompt: {processed_prompt[:200]}...")
            
            # Build payload
            payload = self._build_payload(
                prompt=processed_prompt,
                model=model,
                temperature=temperature or self.config.temperatures.chat,
                format_json=False
            )
            
            # Make the request
            response_data = await self._make_request(
                payload=payload,
                timeout=self.config.timeouts.chat,
                operation_name="chat response"
            )
            
            # Extract and return the response
            chat_response = response_data.get("response", "").strip()
            
            if not chat_response:
                logger.warning("Ollama returned empty chat response")
                return "I'm sorry, I couldn't generate a response. Please try rephrasing your question."
            
            logger.info(f"Generated chat response of {len(chat_response)} characters")
            return chat_response
            
        except OllamaError:
            # Re-raise Ollama-specific errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error in chat response generation: {e}")
            raise OllamaError(f"Chat response generation failed: {str(e)}")
    
    def _preprocess_prompt(self, prompt: str) -> str:
        """
        Preprocess the chat prompt.
        
        Args:
            prompt: Raw prompt to preprocess
            
        Returns:
            Processed prompt ready for the model
        """
        # Clean up whitespace
        prompt = prompt.strip()
        
        # Truncate if too long
        if len(prompt) > self.config.max_content_length:
            logger.info(f"Truncating prompt from {len(prompt)} to {self.config.max_content_length} characters")
            prompt = prompt[:self.config.max_content_length] + "..."
        
        return prompt
