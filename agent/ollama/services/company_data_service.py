"""
Company data extraction service for extracting structured data from Impressum pages.
Handles content preprocessing, multi-model fallback, and data validation.
"""

from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
from utils.logger import logger
from utils.json_utils import extract_json_from_response
from utils.preprocessing_utils import preprocess_impressum_content
from prompts import build_extract_company_data_prompt
from ..base_client import BaseOllamaClient
from ..config import OllamaConfig
from ..exceptions import OllamaError


class CompanyDataExtractionService(BaseOllamaClient):
    """Service for extracting structured company data from Impressum content."""

    # Required fields for company data extraction
    REQUIRED_FIELDS = [
        "company_name",
        "legal_structure",
        "street_address",
        "zip_code",
        "city",
        "country",
        "phone",
        "email",
        "website",
        "managing_director",
        "registration_number",
        "register_court",
        "tax_id",
        "vat_id",
    ]

    def __init__(self, config: Optional[OllamaConfig] = None):
        """Initialize the company data extraction service."""
        super().__init__(config)

    async def extract_company_data(
        self,
        content: str,
        url: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extract structured company data from Impressum content.

        Args:
            content: The text content of the Impressum page
            url: The URL of the website for context
            model: Optional model override

        Returns:
            Dictionary containing extracted company data or error information
        """
        try:
            # Validate inputs
            if not content or not content.strip():
                return {"error": "No content provided for extraction"}

            if not url or not url.strip():
                return {"error": "No URL provided for context"}

            # Extract TLD for context
            tld = self._extract_tld(url)

            # Preprocess content
            cleaned_content = preprocess_impressum_content(content)

            if not cleaned_content or not cleaned_content.strip():
                return {"error": "Content became empty after preprocessing"}

            # Attempt extraction with primary approach
            result = await self._extract_with_primary_approach(
                cleaned_content, tld, model
            )

            # If primary approach failed or returned poor results, try fallback
            if self._should_retry_extraction(result):
                logger.info(
                    "Primary extraction yielded poor results, trying fallback approaches")
                result = await self._extract_with_fallback_approaches(
                    cleaned_content, tld, model
                )

            # Normalize and validate the result
            normalized_result = self._normalize_extraction_result(result)

            # Log extraction quality
            self._log_extraction_quality(normalized_result)

            return normalized_result

        except OllamaError:
            # Re-raise Ollama-specific errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error in company data extraction: {e}")
            return {
                "error": f"Company data extraction failed: {str(e)}",
                "raw_error": str(e)
            }

    async def _extract_with_primary_approach(
        self,
        content: str,
        tld: str,
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Extract data using the primary approach."""
        try:
            # Build extraction prompt
            prompt = build_extract_company_data_prompt(content, tld)

            if self.config.log_prompts and self.config.debug_logging:
                logger.debug(
                    f"Company data extraction prompt: {prompt[:500]}...")

            # Build payload
            payload = self._build_payload(
                prompt=prompt,
                model=model,
                temperature=self.config.temperatures.company_data_extraction,
                format_json=True
            )

            # Make the request
            response_data = await self._make_request(
                payload=payload,
                timeout=self.config.timeouts.company_data_extraction,
                operation_name="company data extraction"
            )

            # Parse the response
            response_text = response_data.get("response", "")
            return extract_json_from_response(response_text)

        except Exception as e:
            logger.error(f"Primary extraction approach failed: {e}")
            return {"error": f"Primary extraction failed: {str(e)}"}

    async def _extract_with_fallback_approaches(
        self,
        content: str,
        tld: str,
        original_model: Optional[str] = None
    ) -> Dict[str, Any]:
        """Try extraction with fallback models and higher temperature."""
        best_result = {"error": "All fallback approaches failed"}
        best_score = 0

        # Try with higher temperature first
        try:
            prompt = build_extract_company_data_prompt(content, tld)
            payload = self._build_payload(
                prompt=prompt,
                model=original_model,
                temperature=self.config.temperatures.company_data_extraction_retry,
                format_json=True
            )

            response_data = await self._make_request(
                payload=payload,
                timeout=self.config.timeouts.company_data_extraction,
                operation_name="company data extraction (retry)"
            )

            result = extract_json_from_response(
                response_data.get("response", ""))
            score = self._score_extraction_result(result)

            if score > best_score:
                best_result = result
                best_score = score

        except Exception as e:
            logger.warning(f"Higher temperature approach failed: {e}")

        # Try with fallback models
        for fallback_model in self.config.models.fallback_models:
            if fallback_model == original_model:
                continue

            try:
                logger.info(
                    f"Trying extraction with fallback model: {fallback_model}")

                prompt = build_extract_company_data_prompt(content, tld)
                payload = self._build_payload(
                    prompt=prompt,
                    model=fallback_model,
                    temperature=self.config.temperatures.company_data_extraction,
                    format_json=True
                )

                response_data = await self._make_request(
                    payload=payload,
                    timeout=self.config.timeouts.company_data_extraction,
                    operation_name=f"company data extraction ({fallback_model})"
                )

                result = extract_json_from_response(
                    response_data.get("response", ""))
                score = self._score_extraction_result(result)

                if score > best_score:
                    best_result = result
                    best_score = score
                    logger.info(
                        f"Better result found with {fallback_model} (score: {score})")

            except Exception as e:
                logger.warning(f"Fallback model {fallback_model} failed: {e}")
                continue

        return best_result

    def _extract_tld(self, url: str) -> str:
        """Extract top-level domain from URL."""
        try:
            parsed_url = urlparse(url)
            netloc = parsed_url.netloc
            if netloc and "." in netloc:
                return netloc.split(".")[-1]
            return "com"  # Default fallback
        except Exception:
            return "com"  # Default fallback

    def _should_retry_extraction(self, result: Dict[str, Any]) -> bool:
        """Determine if extraction should be retried."""
        if not isinstance(result, dict):
            return True

        if "error" in result:
            return True

        # Check if all values are "Not available"
        available_count = sum(
            1 for value in result.values()
            if value and value != "Not available"
        )

        return available_count < 3  # Retry if fewer than 3 fields extracted

    def _score_extraction_result(self, result: Dict[str, Any]) -> int:
        """Score an extraction result based on completeness."""
        if not isinstance(result, dict) or "error" in result:
            return 0

        score = 0
        for field in self.REQUIRED_FIELDS:
            value = result.get(field, "")
            if value and value != "Not available":
                score += 1

        return score

    def _normalize_extraction_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize extraction result by ensuring all required fields exist."""
        if not isinstance(result, dict):
            return {"error": "Invalid extraction result format"}

        if "error" in result:
            return result

        # Ensure all required fields are present
        for field in self.REQUIRED_FIELDS:
            if field not in result or not result[field]:
                result[field] = "Not available"

        return result

    def _log_extraction_quality(self, result: Dict[str, Any]) -> None:
        """Log the quality of the extraction result."""
        if "error" in result:
            logger.error(f"Extraction failed: {result['error']}")
            return

        available_fields = sum(
            1 for value in result.values()
            if value and value != "Not available"
        )

        total_fields = len(self.REQUIRED_FIELDS)
        success_rate = (available_fields / total_fields) * 100

        logger.info(
            f"Extraction completed: {available_fields}/{total_fields} fields "
            f"extracted ({success_rate:.1f}% success rate)"
        )
