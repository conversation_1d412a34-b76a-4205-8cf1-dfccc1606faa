"""
Factory pattern for creating and managing Ollama service instances.
Provides centralized configuration and dependency injection.
"""

from typing import Optional, Dict, Any
from .config import OllamaConfig
from .services.summary_service import SummaryService
from .services.chat_service import ChatService
from .services.command_parsing_service import CommandParsingService
from .services.company_data_service import CompanyDataExtractionService


class OllamaServiceFactory:
    """Factory for creating and managing Ollama service instances."""
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        """
        Initialize the factory with configuration.
        
        Args:
            config: Optional configuration override
        """
        self.config = config or OllamaConfig.create_default()
        self._services: Dict[str, Any] = {}
    
    def get_summary_service(self) -> SummaryService:
        """Get or create a summary service instance."""
        if 'summary' not in self._services:
            self._services['summary'] = SummaryService(self.config)
        return self._services['summary']
    
    def get_chat_service(self) -> ChatService:
        """Get or create a chat service instance."""
        if 'chat' not in self._services:
            self._services['chat'] = ChatService(self.config)
        return self._services['chat']
    
    def get_command_parsing_service(self) -> CommandParsingService:
        """Get or create a command parsing service instance."""
        if 'command_parsing' not in self._services:
            self._services['command_parsing'] = CommandParsingService(self.config)
        return self._services['command_parsing']
    
    def get_company_data_service(self) -> CompanyDataExtractionService:
        """Get or create a company data extraction service instance."""
        if 'company_data' not in self._services:
            self._services['company_data'] = CompanyDataExtractionService(self.config)
        return self._services['company_data']
    
    def clear_cache(self) -> None:
        """Clear the service cache, forcing recreation on next access."""
        self._services.clear()
    
    def update_config(self, config: OllamaConfig) -> None:
        """
        Update the configuration and clear the service cache.
        
        Args:
            config: New configuration to use
        """
        self.config = config
        self.clear_cache()
    
    @classmethod
    def create_default(cls) -> 'OllamaServiceFactory':
        """Create a factory with default configuration."""
        return cls()
    
    @classmethod
    def create_with_config(cls, **config_kwargs) -> 'OllamaServiceFactory':
        """
        Create a factory with custom configuration parameters.
        
        Args:
            **config_kwargs: Configuration parameters to override
            
        Returns:
            Configured factory instance
        """
        config = OllamaConfig.create_default()
        
        # Update configuration with provided kwargs
        for key, value in config_kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            elif hasattr(config.retry, key):
                setattr(config.retry, key, value)
            elif hasattr(config.timeouts, key):
                setattr(config.timeouts, key, value)
            elif hasattr(config.models, key):
                setattr(config.models, key, value)
            elif hasattr(config.temperatures, key):
                setattr(config.temperatures, key, value)
        
        return cls(config)


# Global factory instance for convenience
_default_factory: Optional[OllamaServiceFactory] = None


def get_default_factory() -> OllamaServiceFactory:
    """Get the default global factory instance."""
    global _default_factory
    if _default_factory is None:
        _default_factory = OllamaServiceFactory.create_default()
    return _default_factory


def set_default_factory(factory: OllamaServiceFactory) -> None:
    """Set the default global factory instance."""
    global _default_factory
    _default_factory = factory


def reset_default_factory() -> None:
    """Reset the default factory to None, forcing recreation."""
    global _default_factory
    _default_factory = None
