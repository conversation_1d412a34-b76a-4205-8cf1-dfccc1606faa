# Ollama Client Package
# This package provides a modular, well-structured interface to Ollama services
# with proper separation of concerns and comprehensive error handling.

from .factory import OllamaServiceFactory
from .services.summary_service import SummaryService
from .services.chat_service import ChatService
from .services.command_parsing_service import CommandParsingService
from .services.company_data_service import CompanyDataExtractionService

# Backward compatibility - expose the original functions
from .client import (
    get_summary_from_ollama,
    get_chat_response_from_ollama,
    parse_command_from_prompt,
    extract_company_data_from_impressum,
)

__all__ = [
    'OllamaServiceFactory',
    'SummaryService',
    'ChatService', 
    'CommandParsingService',
    'CompanyDataExtractionService',
    'get_summary_from_ollama',
    'get_chat_response_from_ollama',
    'parse_command_from_prompt',
    'extract_company_data_from_impressum',
]
