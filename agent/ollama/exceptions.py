"""
Custom exceptions for the Ollama client package.
Provides structured error handling with specific exception types.
"""


class OllamaError(Exception):
    """Base exception for all Ollama-related errors."""
    pass


class OllamaConnectionError(OllamaError):
    """Raised when connection to Ollama server fails."""
    pass


class OllamaTimeoutError(OllamaError):
    """Raised when Ollama request times out."""
    pass


class OllamaModelNotFoundError(OllamaError):
    """Raised when the specified model is not available."""
    pass


class OllamaResponseError(OllamaError):
    """Raised when Ollama returns an invalid or unexpected response."""
    pass


class OllamaValidationError(OllamaError):
    """Raised when input validation fails."""
    pass


class OllamaParsingError(OllamaError):
    """Raised when JSON parsing of Ollama response fails."""
    pass
