"""
Configuration management for Ollama services.
Centralizes all configuration settings with sensible defaults.
"""

from dataclasses import dataclass, field
from typing import List, Optional
from config.settings import OLLAMA_MODEL, OLLAMA_URL


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0


@dataclass
class TimeoutConfig:
    """Configuration for request timeouts."""
    summary: int = 120
    chat: int = 120
    command_parsing: int = 120
    company_data_extraction: int = 180


@dataclass
class ModelConfig:
    """Configuration for model selection and fallbacks."""
    primary_model: str = OLLAMA_MODEL
    fallback_models: List[str] = field(default_factory=lambda: ["llama3", "qwen3", "mistral"])
    
    def get_all_models(self) -> List[str]:
        """Get all models including primary and fallbacks."""
        models = [self.primary_model]
        for model in self.fallback_models:
            if model not in models:
                models.append(model)
        return models


@dataclass
class TemperatureConfig:
    """Configuration for model temperature settings."""
    summary: float = 0.1
    chat: float = 0.5
    command_parsing: float = 0.0
    company_data_extraction: float = 0.2
    company_data_extraction_retry: float = 0.7


@dataclass
class OllamaConfig:
    """Main configuration class for Ollama services."""
    base_url: str = OLLAMA_URL
    retry: RetryConfig = field(default_factory=RetryConfig)
    timeouts: TimeoutConfig = field(default_factory=TimeoutConfig)
    models: ModelConfig = field(default_factory=ModelConfig)
    temperatures: TemperatureConfig = field(default_factory=TemperatureConfig)
    
    # Content processing limits
    max_content_length: int = 50000
    truncated_content_length: int = 5000
    
    # Logging configuration
    debug_logging: bool = True
    log_prompts: bool = True
    log_responses: bool = True
    
    @classmethod
    def create_default(cls) -> 'OllamaConfig':
        """Create a default configuration instance."""
        return cls()
    
    def validate(self) -> None:
        """Validate the configuration settings."""
        if not self.base_url:
            raise ValueError("base_url cannot be empty")
        
        if self.retry.max_attempts < 1:
            raise ValueError("max_attempts must be at least 1")
        
        if not self.models.primary_model:
            raise ValueError("primary_model cannot be empty")
        
        # Validate temperature ranges
        for temp_name, temp_value in [
            ("summary", self.temperatures.summary),
            ("chat", self.temperatures.chat),
            ("command_parsing", self.temperatures.command_parsing),
            ("company_data_extraction", self.temperatures.company_data_extraction),
        ]:
            if not 0.0 <= temp_value <= 2.0:
                raise ValueError(f"{temp_name} temperature must be between 0.0 and 2.0")
