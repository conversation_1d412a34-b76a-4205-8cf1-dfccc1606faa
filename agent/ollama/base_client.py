"""
Base HTTP client for Ollama services.
Provides common functionality for making HTTP requests with retry logic,
timeout handling, and error management.
"""

import asyncio
from typing import Dict, Any, Optional
import httpx
from utils.logger import logger
from .config import OllamaConfig, RetryConfig
from .exceptions import (
    OllamaConnectionError,
    OllamaTimeoutError,
    OllamaModelNotFoundError,
    OllamaResponseError,
)


class BaseOllamaClient:
    """Base client for making requests to Ollama API."""
    
    def __init__(self, config: Optional[OllamaConfig] = None):
        """Initialize the base client with configuration."""
        self.config = config or OllamaConfig.create_default()
        self.config.validate()
    
    async def _make_request(
        self,
        payload: Dict[str, Any],
        timeout: int,
        retry_config: Optional[RetryConfig] = None,
        operation_name: str = "request"
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to Ollama with retry logic and error handling.
        
        Args:
            payload: The request payload
            timeout: Request timeout in seconds
            retry_config: Optional custom retry configuration
            operation_name: Name of the operation for logging
            
        Returns:
            The response JSON as a dictionary
            
        Raises:
            OllamaConnectionError: When connection fails
            OllamaTimeoutError: When request times out
            OllamaModelNotFoundError: When model is not found
            OllamaResponseError: When response is invalid
        """
        retry_config = retry_config or self.config.retry
        last_exception = None
        
        for attempt in range(retry_config.max_attempts):
            try:
                if self.config.debug_logging:
                    logger.info(f"Making {operation_name} request (attempt {attempt + 1}/{retry_config.max_attempts})")
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        self.config.base_url,
                        json=payload,
                        timeout=timeout
                    )
                    response.raise_for_status()
                    
                    response_data = response.json()
                    if self.config.log_responses and self.config.debug_logging:
                        response_text = response_data.get("response", "")
                        logger.debug(f"{operation_name} response: {response_text[:200]}...")
                    
                    return response_data
                    
            except httpx.ReadTimeout as e:
                last_exception = OllamaTimeoutError(f"{operation_name} request timed out after {timeout}s")
                if attempt < retry_config.max_attempts - 1:
                    delay = min(
                        retry_config.base_delay * (retry_config.exponential_base ** attempt),
                        retry_config.max_delay
                    )
                    logger.warning(f"{operation_name} timed out, retrying in {delay}s...")
                    await asyncio.sleep(delay)
                    continue
                    
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 404:
                    # Model not found - try fallback if available
                    if attempt == 0 and len(self.config.models.fallback_models) > 0:
                        logger.warning(f"Model {payload['model']} not found, trying fallback...")
                        payload["model"] = self.config.models.fallback_models[0]
                        continue
                    else:
                        raise OllamaModelNotFoundError(f"Model {payload['model']} not found")
                elif e.response.status_code == 400:
                    # Bad request - might be content too long
                    if attempt == 0 and "prompt" in payload:
                        logger.warning("Bad request, trying with truncated content...")
                        payload["prompt"] = self._truncate_prompt(payload["prompt"])
                        continue
                    else:
                        raise OllamaResponseError(f"Bad request: {e.response.text}")
                else:
                    last_exception = OllamaConnectionError(f"HTTP {e.response.status_code}: {e.response.text}")
                    
            except httpx.RequestError as e:
                last_exception = OllamaConnectionError(f"Connection error: {str(e)}")
                
            except Exception as e:
                last_exception = OllamaResponseError(f"Unexpected error: {str(e)}")
            
            # If we get here, we had an error and should retry (if attempts remain)
            if attempt < retry_config.max_attempts - 1:
                delay = min(
                    retry_config.base_delay * (retry_config.exponential_base ** attempt),
                    retry_config.max_delay
                )
                logger.warning(f"{operation_name} failed, retrying in {delay}s...")
                await asyncio.sleep(delay)
        
        # All retries exhausted
        if last_exception:
            raise last_exception
        else:
            raise OllamaConnectionError(f"{operation_name} failed after {retry_config.max_attempts} attempts")
    
    def _truncate_prompt(self, prompt: str) -> str:
        """Truncate a prompt to fit within limits."""
        if len(prompt) <= self.config.truncated_content_length:
            return prompt
        
        # Try to truncate at a reasonable boundary
        truncated = prompt[:self.config.truncated_content_length]
        
        # Find the last complete sentence or paragraph
        for delimiter in ['\n\n', '\n', '. ', '! ', '? ']:
            last_pos = truncated.rfind(delimiter)
            if last_pos > self.config.truncated_content_length // 2:
                return truncated[:last_pos + len(delimiter)]
        
        # If no good boundary found, just truncate
        return truncated + "..."
    
    def _build_payload(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: float = 0.5,
        format_json: bool = False,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Build a standard payload for Ollama requests."""
        payload = {
            "model": model or self.config.models.primary_model,
            "prompt": prompt,
            "stream": stream,
            "temperature": temperature,
        }
        
        if format_json:
            payload["format"] = "json"
        
        return payload
