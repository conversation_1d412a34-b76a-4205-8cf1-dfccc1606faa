"""
LangGraph Terminal Interface for Crawl4AI Agent

This module provides a natural language terminal interface that uses
the LangGraph agent to process user requests.
"""

import asyncio
import j<PERSON>
from typing import Dict, Any
from agent.langgraph_workflow import CrawlAgent
from utils.logger import logger


class LangGraphTerminal:
    """
    Terminal interface for the LangGraph-based Crawl4AI agent.
    """

    def __init__(self):
        """Initialize the terminal with the LangGraph agent."""
        self.agent = CrawlAgent()
        self.thread_id = "terminal_session"

    def display_welcome(self):
        """Display welcome message and usage instructions."""
        print("🚀 Crawl4AI LangGraph Agent - Natural Language Interface")
        print("=" * 60)
        print("You can now use natural language to interact with the crawler!")
        print()
        print("Examples:")
        print("  • crawl imprint from example.com")
        print("  • extract legal info from local example.com")
        print("  • summarize https://example.com --screenshot")
        print("  • find company data for example.com")
        print("  • smart crawl https://example.com for product data")
        print("  • extract sitemap from docs.python.org")
        print("  • get sitemap from https://example.com")
        print("  • search for python web scraping")
        print("  • search and crawl best web crawlers")
        print("  • process local file /path/to/file.html")
        print("  • what can you do?")
        print()
        print("Features:")
        print("  • Natural language understanding")
        print("  • Automatic intent parsing")
        print("  • AI-powered schema learning for structured data extraction")
        print("  • Sitemap extraction and URL discovery")
        print("  • Web search with DuckDuckGo")
        print("  • Search and crawl top results")
        print("  • Support for screenshots (--screenshot) and PDFs (--pdf)")
        print("  • Local file processing")
        print("  • Company data extraction")
        print("  • Content summarization")
        print()
        print("Type 'exit' to quit, 'help' for more info, or 'graph' to see workflow.")
        print("=" * 60)
        print()

    def display_help(self):
        """Display detailed help information."""
        print("\n📖 Crawl4AI LangGraph Agent - Help")
        print("=" * 50)
        print()
        print("NATURAL LANGUAGE COMMANDS:")
        print("  The agent understands natural language! Just describe what you want:")
        print()
        print("  Website Crawling:")
        print("    • 'crawl example.com'")
        print("    • 'get content from https://example.com'")
        print("    • 'fetch website example.com with screenshot'")
        print()
        print("  Legal/Impressum Extraction:")
        print("    • 'find impressum from example.com'")
        print("    • 'extract legal info from example.com'")
        print("    • 'get company data from example.com'")
        print()
        print("  Content Summarization:")
        print("    • 'summarize example.com'")
        print("    • 'create summary of https://example.com'")
        print()
        print("  Web Search:")
        print("    • 'search for python web scraping'")
        print("    • 'find information about AI agents'")
        print("    • 'search and crawl best web crawlers'")
        print()
        print("  Local File Processing:")
        print("    • 'process local file /path/to/file.html'")
        print("    • 'extract data from local example.com'")
        print("    • 'summarize local file /path/to/file.html'")
        print()
        print("  Media Options:")
        print("    • Add '--screenshot' to capture screenshots")
        print("    • Add '--pdf' to generate PDFs")
        print("    • Example: 'crawl example.com --screenshot --pdf'")
        print()
        print("  General Chat:")
        print("    • 'what can you do?'")
        print("    • 'how does web crawling work?'")
        print("    • 'explain impressum extraction'")
        print()
        print("SPECIAL COMMANDS:")
        print("  • 'exit' - Quit the application")
        print("  • 'help' - Show this help message")
        print("  • 'graph' - Display workflow visualization")
        print("  • 'clear' - Clear the screen")
        print()
        print("=" * 50)
        print()

    def display_result(self, result: Dict[str, Any]):
        """
        Display the result from the agent in a user-friendly format.

        Args:
            result: Result dictionary from the agent
        """
        if result and "error" in result:
            print(f"❌ Error: {result['error']}")
            return

        if not result:
            print("⚠️  No result data available")
            return

        print("✅ Success!")
        print("-" * 40)

        # Display summary if available
        if result and "summary" in result:
            print("📄 Summary:")
            print(result["summary"])
            print()

        # Display company data if available
        if result and "company_data" in result:
            print("🏢 Company Data:")
            company_data = result["company_data"]
            if isinstance(company_data, dict):
                for key, value in company_data.items():
                    if value:  # Only show non-empty values
                        print(f"  • {key.replace('_', ' ').title()}: {value}")
            else:
                print(f"  {company_data}")
            print()

        # Display content if available (and no summary)
        if result and "content" in result and "summary" not in result:
            content = result["content"]
            if len(content) > 500:
                print("📝 Content (first 500 characters):")
                print(content[:500] + "...")
            else:
                print("📝 Content:")
                print(content)
            print()

        # Display search results if available
        if result and "search_results" in result:
            print("🔍 Search Results:")
            search_results = result["search_results"]
            # Show top 5
            for i, search_result in enumerate(search_results[:5], 1):
                print(f"  {i}. {search_result.get('title', 'No title')}")
                print(f"     URL: {search_result.get('url', 'No URL')}")
                snippet = search_result.get('snippet', 'No description')
                if len(snippet) > 100:
                    snippet = snippet[:100] + "..."
                print(f"     {snippet}")
                print()

        # Display crawled search results if available
        if result and "crawled_search_results" in result:
            print("🕷️ Crawled Search Results:")
            crawled_results = result["crawled_search_results"]
            successful_crawls = [
                r for r in crawled_results if "error" not in r]
            failed_crawls = [r for r in crawled_results if "error" in r]

            print(
                f"  Successfully crawled: {len(successful_crawls)}/{len(crawled_results)} pages")

            # Show top 3 successful crawls
            for crawled_result in successful_crawls[:3]:
                print(f"  • {crawled_result.get('title', 'No title')}")
                print(f"    URL: {crawled_result.get('url', 'No URL')}")
                content = crawled_result.get('markdown_content', '')
                if len(content) > 200:
                    content = content[:200] + "..."
                print(f"    Content: {content}")
                if crawled_result.get('saved_html_path'):
                    print(f"    Saved: {crawled_result['saved_html_path']}")
                print()

            if failed_crawls:
                print(f"  ⚠️ Failed to crawl {len(failed_crawls)} pages")

        # Display multiple impressum results if available
        if result and "multiple_impressum_results" in result:
            print("🏢 Multiple Impressum Results:")
            impressum_results = result["multiple_impressum_results"]
            successful_results = [
                r for r in impressum_results if r.get("success")]
            failed_results = [
                r for r in impressum_results if not r.get("success")]

            print(
                f"  📊 Total: {len(impressum_results)} | ✅ Success: {len(successful_results)} | ❌ Failed: {len(failed_results)}")
            print()

            # Show successful results
            for i, impressum_result in enumerate(successful_results, 1):
                print(f"  {i}. ✅ {impressum_result.get('url', 'Unknown URL')}")
                content = impressum_result.get('markdown_content', '')
                if content and len(content) > 200:
                    content = content[:200] + "..."
                if content:
                    print(f"     📝 Content: {content}")
                if impressum_result.get('html_path'):
                    print(f"     💾 Saved: {impressum_result['html_path']}")
                print()

            # Show failed results
            if failed_results:
                print("  ❌ Failed URLs:")
                for impressum_result in failed_results:
                    print(
                        f"    • {impressum_result.get('url', 'Unknown URL')}: {impressum_result.get('error', 'Unknown error')}")
                print()

        # Display multiple crawl results if available
        if result and "multiple_crawl_results" in result:
            print("🌐 Multiple Crawl Results:")
            crawl_results = result["multiple_crawl_results"]
            successful_results = [r for r in crawl_results if r.get("success")]
            failed_results = [r for r in crawl_results if not r.get("success")]

            print(
                f"  📊 Total: {len(crawl_results)} | ✅ Success: {len(successful_results)} | ❌ Failed: {len(failed_results)}")
            print()

            # Show successful results
            for i, crawl_result in enumerate(successful_results, 1):
                print(f"  {i}. ✅ {crawl_result.get('url', 'Unknown URL')}")
                if crawl_result.get('page_title'):
                    print(f"     📄 Title: {crawl_result['page_title']}")
                content = crawl_result.get('markdown_content', '')
                if content and len(content) > 200:
                    content = content[:200] + "..."
                if content:
                    print(f"     📝 Content: {content}")
                if crawl_result.get('html_path'):
                    print(f"     💾 Saved: {crawl_result['html_path']}")
                print()

            # Show failed results
            if failed_results:
                print("  ❌ Failed URLs:")
                for crawl_result in failed_results:
                    print(
                        f"    • {crawl_result.get('url', 'Unknown URL')}: {crawl_result.get('error', 'Unknown error')}")
                print()

        # Display chat response if available
        if result and "response" in result:
            print("💬 Response:")
            print(result["response"])
            print()

        # Display file paths if available
        if result and "files" in result:
            print("📁 Saved Files:")
            files = result["files"]
            for file_type, path in files.items():
                if path:
                    print(f"  • {file_type.title()}: {path}")
            print()

    def display_workflow_graph(self):
        """Display information about the workflow graph."""
        try:
            graph = self.agent.get_workflow_graph()
            print("\n🔄 LangGraph Workflow Structure:")
            print("=" * 40)
            print("The agent uses the following workflow nodes:")
            print()
            print("1. Intent Parser - Understands your natural language request")
            print("2. URL Crawler - Crawls websites and captures content")
            print("3. Impressum Finder - Finds and crawls legal notice pages")
            print("4. Local File Processor - Processes local HTML files")
            print("5. Web Searcher - Searches the web using DuckDuckGo")
            print("6. Search and Crawler - Searches and crawls top results")
            print("7. Data Extractor - Extracts structured company data")
            print("8. Summarizer - Generates content summaries")
            print("9. Result Formatter - Formats final output")
            print("10. Chat Responder - Handles general questions")
            print()
            print("The workflow automatically routes between these nodes")
            print("based on your request and the available data.")
            print("=" * 40)
            print()
        except Exception as e:
            print(f"❌ Error displaying workflow: {e}")

    async def run(self):
        """Run the interactive terminal loop."""
        self.display_welcome()

        while True:
            try:
                # Get user input
                user_input = input("🧠 Your request: ").strip()

                # Handle special commands
                if user_input.lower() == "exit":
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == "help":
                    self.display_help()
                    continue
                elif user_input.lower() == "graph":
                    self.display_workflow_graph()
                    continue
                elif user_input.lower() == "clear":
                    print("\033[2J\033[H")  # Clear screen
                    continue
                elif not user_input:
                    continue

                # Process the request through the LangGraph agent
                print("🔄 Processing your request...")
                result = await self.agent.process_request(user_input, self.thread_id)

                # Display the result
                try:
                    self.display_result(result)
                except Exception as display_error:
                    logger.error(f"Display error: {display_error}")
                    import traceback
                    traceback.print_exc()
                    print(f"❌ Error displaying result: {display_error}")
                    print(f"📋 Result type: {type(result)}")
                    if result:
                        print(
                            f"📋 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                    else:
                        print("📋 Result is None or empty")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                logger.error(f"Terminal error: {e}")
                print(f"❌ An unexpected error occurred: {e}")
                print("Please try again or type 'help' for assistance.")


async def main():
    """Main entry point for the LangGraph terminal interface."""
    terminal = LangGraphTerminal()
    await terminal.run()


if __name__ == "__main__":
    asyncio.run(main())
