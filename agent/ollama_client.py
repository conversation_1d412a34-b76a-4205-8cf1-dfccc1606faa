# ollama_client.py - DEPRECATED
# This file has been refactored into the agent.ollama package
# Please use: from agent.ollama import get_summary_from_ollama, etc.
# This file is kept for backward compatibility and will be removed in a future version.

import warnings
warnings.warn(
    "agent.ollama_client is deprecated. Use agent.ollama instead.",
    DeprecationWarning,
    stacklevel=2
)

# Import from the new modular structure for backward compatibility
from agent.ollama import (
    get_summary_from_ollama,
    get_chat_response_from_ollama,
    parse_command_from_prompt,
    extract_company_data_from_impressum,
)

# All functions are now imported from agent.ollama for backward compatibility
