"""
This module defines the high-level commands that the agent can execute,
such as summarizing a website or extracting data from a legal notice (Impressum).
"""
from crawler import crawl_website, crawl_local_file
from crawler.company_data import crawl_impressum
from utils.file_utils import save_json_to_dir, save_summary_to_dir
from utils.preprocessing_utils import preprocess_impressum_content, extract_text_from_html
from agent.ollama import (
    get_summary_from_ollama,
    extract_company_data_from_impressum,
)
from utils.logger import logger
import os


async def summarize_website(url: str, screenshot: bool = False, pdf: bool = False) -> dict:
    """
    Orchestrates the process of crawling a website and generating a summary.
    Optionally captures screenshots and PDFs.

    Args:
        url (str): The URL of the website to summarize
        screenshot (bool): Whether to capture a screenshot
        pdf (bool): Whether to generate a PDF

    Returns:
        A dictionary containing the summary and paths to any media files
    """
    logger.info(f"Starting website summarization for: {url}")
    # Step 1: Crawl the website to get its content and save the raw HTML.
    content, saved_html_path, screenshot_path, pdf_path = await crawl_website(
        url, screenshot=screenshot, pdf=pdf
    )

    if content.startswith("Error:"):
        logger.error(f"Crawling failed for {url}: {content}")
        return {"error": content}

    if saved_html_path:
        logger.info(f"Raw HTML saved to: {saved_html_path}")

    logger.info(
        "Content crawled successfully. Requesting summary from Ollama...")
    # Step 2: Send the crawled content to the Ollama client for summarization.
    summary = await get_summary_from_ollama(content)

    if summary.startswith("Error"):
        logger.error(f"Summarization failed: {summary}")
        return {"error": summary}

    summary_path = save_summary_to_dir(summary, url)
    logger.info(f"Summary saved to: {summary_path}")

    # Return all results in a structured format
    return {
        "summary": summary,
        "files": {
            "html": saved_html_path,
            "summary": summary_path,
            "screenshot": screenshot_path,
            "pdf": pdf_path
        }
    }


async def extract_impressum(base_url: str) -> dict | None:
    """
    Orchestrates finding an Impressum page, extracting structured company data,
    and saving it to a file.

    Args:
        base_url (str): The base URL of the website to search for an Impressum.

    Returns:
        A dictionary containing the extracted company data, or None if an error occurs.
    """
    logger.info(f"Starting Impressum extraction for: {base_url}")
    # Step 1: Attempt to find, save, and crawl the Impressum page.
    impressum_content, saved_html_path = await crawl_impressum(base_url)

    if not impressum_content:
        logger.warning(f"Could not find an Impressum page for {base_url}")
        return {"error": f"Could not find an Impressum/Legal Notice page on {base_url}"}

    if impressum_content.startswith("Error:"):
        logger.error(
            f"Crawling Impressum failed for {base_url}: {impressum_content}")
        return {"error": impressum_content}

    if saved_html_path:
        logger.info(f"Impressum HTML saved to: {saved_html_path}")

    # Check if the content is too short to likely contain company data
    if len(impressum_content) < 100:
        logger.warning(
            f"Impressum content for {base_url} is suspiciously short ({len(impressum_content)} chars)")
        return {"error": "The Impressum page content is too short to contain company data"}

    logger.info(
        f"Impressum content crawled ({len(impressum_content)} chars). Extracting company data...")

    # Step 2: Send the Impressum content to Ollama to extract structured data.
    company_data = await extract_company_data_from_impressum(
        content=impressum_content, url=base_url
    )

    # If extraction returns an error, we still return the dict with the error message.
    if "error" in company_data:
        logger.error(f"Data extraction failed: {company_data.get('error')}")
        return company_data

    # Check if we got meaningful data (not all "Not available")
    available_fields = sum(
        1 for value in company_data.values() if value != "Not available")
    if available_fields <= 1:  # Changed from 0 to 1 to catch cases with only one field
        logger.warning(
            f"Extraction returned only {available_fields} usable fields")

        # Try to read the HTML file directly as a fallback
        if saved_html_path and os.path.exists(saved_html_path):
            logger.info("Trying direct HTML extraction as fallback...")
            try:
                with open(saved_html_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                # Extract text from HTML using our specialized function
                extracted_text = extract_text_from_html(html_content)

                # Try extraction again with the extracted text
                company_data = await extract_company_data_from_impressum(
                    content=extracted_text, url=base_url
                )

                # Check if this improved the results
                available_fields = sum(
                    1 for value in company_data.values() if value != "Not available")
                if available_fields <= 1:
                    return {
                        "error": "Could not extract sufficient company data from the page",
                        "troubleshooting": "The page might not be a proper Impressum/Legal Notice, or the content format is not recognized",
                        "partial_data": company_data
                    }
            except Exception as e:
                logger.error(f"Error in fallback extraction: {e}")
                return {
                    "error": "Could not extract any company data from the page",
                    "troubleshooting": f"Fallback extraction failed: {str(e)}"
                }

    # Step 3: Save the extracted data to a JSON file.
    file_path = save_json_to_dir(company_data, base_url)
    logger.info(f"Company data saved to: {file_path}")

    return company_data


async def process_local_file(file_path: str) -> str | None:
    """
    Orchestrates the process of crawling a local file and returning its content.

    Args:
        file_path (str): The local path to the file to process.

    Returns:
        The extracted content as a string, or None if an error occurs.
    """
    logger.info(f"Starting local file processing for: {file_path}")
    # Step 1: Crawl the local file to get its content.
    content = await crawl_local_file(file_path)

    if not content:
        logger.error(
            f"Processing failed for {file_path}. Content could not be extracted."
        )
        return None

    return content


async def extract_sitemap(url: str) -> dict:
    """
    Orchestrates the process of extracting URLs from a website's sitemap.

    Args:
        url (str): The URL of the website to extract sitemap from

    Returns:
        A dictionary containing the sitemap extraction results
    """
    logger.info(f"Starting sitemap extraction for: {url}")

    try:
        # Import here to avoid circular imports
        from crawler.sitemap import read_sitemap

        # Extract sitemap
        result = await read_sitemap(url, timeout=30)

        if result['urls']:
            logger.info(
                f"Successfully extracted {len(result['urls'])} URLs from sitemap")

            # Save the sitemap data to a JSON file
            sitemap_data = {
                "base_url": url,
                "sitemap_source": result['source'],
                "extraction_timestamp": __import__('datetime').datetime.now().isoformat(),
                "total_urls": len(result['urls']),
                "urls": result['urls']
            }

            file_path = save_json_to_dir(sitemap_data, url, "sitemap")
            logger.info(f"Sitemap data saved to: {file_path}")

            return {
                "success": True,
                "sitemap_source": result['source'],
                "total_urls": len(result['urls']),
                "urls": result['urls'][:20],  # Show first 20 URLs in response
                "file_path": file_path,
                "message": f"Successfully extracted {len(result['urls'])} URLs from sitemap"
            }
        else:
            logger.warning(f"No sitemap found for {url}")
            return {
                "success": False,
                "error": f"No sitemap found for {url}",
                "message": "The website doesn't appear to have a sitemap.xml file or sitemap entries in robots.txt"
            }

    except Exception as e:
        error_msg = f"Error extracting sitemap from {url}: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "message": "Failed to extract sitemap due to an error"
        }
