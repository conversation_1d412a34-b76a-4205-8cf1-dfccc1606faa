import configparser
import os
import logging

# --- Configuration Loading ---

# Create a ConfigParser object to read the INI file.
config = configparser.ConfigParser()

# Define the path to the config file, which should be in the project's root directory.
# This makes the path relative to this file's location.
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
config_file_path = os.path.join(project_root, 'config.ini')

# Check if the config file exists. If not, raise an error with instructions.
if not os.path.exists(config_file_path):
    raise FileNotFoundError(
        f"Configuration file not found at '{config_file_path}'.\n"
        "Please copy 'config.ini.example' to 'config.ini' and customize it."
    )

# Read the configuration from the file.
config.read(config_file_path)


# --- Ollama Settings ---

OLLAMA_URL = config.get('ollama', 'url')
OLLAMA_MODEL = config.get('ollama', 'model')

# --- Logging Settings ---
# Read the log level from the config, defaulting to 'INFO'.
# The `getattr` call safely converts the string (e.g., "DEBUG") to the logging constant (e.g., logging.DEBUG).
LOG_LEVEL_STR = config.get('logging', 'level', fallback='INFO').upper()
LOG_LEVEL = getattr(logging, LOG_LEVEL_STR, logging.INFO)