#!/usr/bin/env python3
"""
Historical Data Import CLI

This script provides a command-line interface for importing historical
crawl data into the statistics database.
"""

from utils.logger import logger
from crawler.statistics.historical_importer import HistoricalDataImporter
import asyncio
import argparse
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    import logging

    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('historical_import.log')
        ]
    )


def print_preview_summary(preview: dict):
    """Print a formatted preview summary."""
    print("\n" + "="*60)
    print("📊 HISTORICAL DATA IMPORT PREVIEW")
    print("="*60)

    # Use the actual structure returned by HistoricalDataImporter
    print(
        f"📈 Estimated records to import: {preview.get('estimated_records', 0):,}")
    print(f"🌐 Domains found: {len(preview.get('domains_found', [])):,}")
    print(f"📁 File types found: {len(preview.get('file_types', {})):,}")

    date_range = preview.get('date_range', {})
    if date_range.get('earliest') and date_range.get('latest'):
        earliest = date_range['earliest']
        latest = date_range['latest']
        # Handle both datetime objects and strings
        earliest_str = earliest.strftime(
            '%Y-%m-%d') if hasattr(earliest, 'strftime') else str(earliest)[:10]
        latest_str = latest.strftime(
            '%Y-%m-%d') if hasattr(latest, 'strftime') else str(latest)[:10]
        print(f"📅 Date range: {earliest_str} to {latest_str}")

    print(f"\n📂 DIRECTORIES ANALYZED:")
    directories = preview.get('directories_found', {})
    for dir_name, info in directories.items():
        status = "✅" if info.get('exists') else "❌"
        domain_count = len(info.get('domains', [])) if isinstance(
            info.get('domains'), list) else len(info.get('domains', set()))
        print(
            f"  {status} {dir_name}: {info.get('file_count', 0):,} files, {domain_count:,} domains")

    print(f"\n🏢 TOP DOMAINS:")
    domains_found = preview.get('domains_found', [])
    top_domains = domains_found[:10] if isinstance(
        domains_found, list) else list(domains_found)[:10]
    for i, domain in enumerate(top_domains, 1):
        print(f"  {i:2d}. {domain}")

    print(f"\n📄 FILE TYPES:")
    file_types = preview.get('file_types', {})
    for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {ext or 'no extension'}: {count:,} files")

    print("="*60)


def print_import_results(results: dict, dry_run: bool = False):
    """Print formatted import results."""
    action = "WOULD BE IMPORTED" if dry_run else "IMPORTED"

    print("\n" + "="*60)
    print(f"📊 HISTORICAL DATA {action}")
    print("="*60)

    print(f"📝 Log files processed: {results.get('logs_processed', 0):,}")
    print(
        f"📊 Crawl records {action.lower()}: {results.get('crawl_records_imported', 0):,}")
    print(f"🌐 Domains discovered: {results.get('domains_discovered', 0):,}")
    print(f"❌ Errors encountered: {results.get('errors', 0):,}")

    if not dry_run and results.get('crawl_records_imported', 0) > 0:
        print(f"\n✅ Historical data successfully imported!")
        print(f"   You can now run 'show crawl statistics' to see the imported data.")
    elif dry_run:
        print(f"\n💡 This was a dry run. Use --import to actually import the data.")

    print("="*60)


async def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description='Import historical crawl data into statistics database',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python import_historical_data.py --preview          # Show what would be imported
  python import_historical_data.py --import           # Actually import the data
  python import_historical_data.py --import --verbose # Import with detailed logging
        """
    )

    parser.add_argument('--preview', action='store_true',
                        help='Show preview of what would be imported')
    parser.add_argument('--import', action='store_true', dest='do_import',
                        help='Actually import the historical data')
    parser.add_argument('--dry-run', action='store_true',
                        help='Perform a dry run (same as --preview)')
    parser.add_argument('--project-root', type=str, default='.',
                        help='Project root directory (default: current directory)')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose logging')

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.verbose)

    # Validate arguments
    if not (args.preview or args.do_import or args.dry_run):
        print("❌ Error: You must specify either --preview or --import")
        parser.print_help()
        return 1

    try:
        # Initialize importer
        importer = HistoricalDataImporter(args.project_root)

        if args.preview or args.dry_run:
            # Show preview
            print("🔍 Analyzing historical data...")
            preview = importer.get_import_preview()
            print_preview_summary(preview)

            if not args.do_import:
                return 0

        if args.do_import:
            # Perform import
            dry_run = args.dry_run
            action = "Analyzing" if dry_run else "Importing"
            print(f"📥 {action} historical data...")

            results = importer.import_all_historical_data(dry_run=dry_run)
            print_import_results(results, dry_run)

            if results.get('errors', 0) > 0:
                print(
                    f"\n⚠️  Some errors occurred during import. Check the logs for details.")
                return 1

        return 0

    except KeyboardInterrupt:
        print("\n❌ Import cancelled by user")
        return 1
    except Exception as e:
        logger.error(f"Import failed: {e}", exc_info=True)
        print(f"❌ Import failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(asyncio.run(main()))
