"""
Comprehensive tests for crawler statistics functionality.

This module tests all statistics functionality including database operations,
tracking accuracy, and analysis functions.
"""

import pytest
import asyncio
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from typing import List, Dict

# Import the statistics components to test
from crawler.statistics.models import CrawlRecord, DomainStats, PageStats, CrawlStatus, CrawlType
from crawler.statistics.database import StatisticsDatabase
from crawler.statistics.tracker import StatisticsTracker, track_crawl
from crawler.statistics.analyzer import StatisticsAnalyzer, generate_domain_report
from crawler.statistics.tools import (
    get_crawl_statistics_summary,
    analyze_domain_performance,
    StatisticsReportInput,
    DomainAnalysisInput
)


class TestCrawlRecord:
    """Test CrawlRecord model validation and functionality."""
    
    def test_crawl_record_creation(self):
        """Test creating a valid CrawlRecord."""
        record = CrawlRecord(
            url="https://example.com/page",
            crawl_type=CrawlType.WEBSITE,
            status=CrawlStatus.SUCCESS,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(seconds=2),
            content_length=1024,
            response_code=200
        )
        
        assert record.url == "https://example.com/page"
        assert record.domain == "example.com"
        assert record.page_path == "/page"
        assert record.crawl_type == CrawlType.WEBSITE
        assert record.status == CrawlStatus.SUCCESS
        assert record.content_length == 1024
        assert record.response_code == 200
    
    def test_crawl_record_domain_extraction(self):
        """Test automatic domain extraction from URL."""
        record = CrawlRecord(
            url="https://www.example.com/test/page?param=value",
            crawl_type=CrawlType.WEBSITE,
            status=CrawlStatus.SUCCESS,
            start_time=datetime.now()
        )
        
        assert record.domain == "example.com"  # www. should be stripped
        assert record.page_path == "/test/page"
    
    def test_crawl_record_invalid_url(self):
        """Test validation of invalid URLs."""
        with pytest.raises(ValueError):
            CrawlRecord(
                url="not-a-valid-url",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS,
                start_time=datetime.now()
            )


class TestStatisticsDatabase:
    """Test StatisticsDatabase functionality."""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        db = StatisticsDatabase(db_path)
        yield db
        
        # Cleanup
        db.close()
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    def test_database_initialization(self, temp_db):
        """Test database initialization and schema creation."""
        # Database should be initialized without errors
        assert temp_db is not None
        
        # Test that we can get a connection
        conn = temp_db._get_connection()
        assert conn is not None
    
    def test_record_crawl(self, temp_db):
        """Test recording a crawl operation."""
        record = CrawlRecord(
            url="https://example.com/test",
            crawl_type=CrawlType.WEBSITE,
            status=CrawlStatus.SUCCESS,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(seconds=1.5),
            content_length=2048,
            response_code=200
        )
        
        record_id = temp_db.record_crawl(record)
        assert record_id > 0
        
        # Verify the record was stored
        retrieved_record = temp_db.get_crawl_record(record_id)
        assert retrieved_record is not None
        assert retrieved_record.url == record.url
        assert retrieved_record.status == record.status
    
    def test_domain_stats_aggregation(self, temp_db):
        """Test that domain statistics are properly aggregated."""
        # Record multiple crawls for the same domain
        domain = "example.com"
        for i in range(3):
            record = CrawlRecord(
                url=f"https://{domain}/page{i}",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS if i < 2 else CrawlStatus.FAILED,
                start_time=datetime.now(),
                content_length=1000 + i * 100
            )
            temp_db.record_crawl(record)
        
        # Check domain statistics
        domain_stats = temp_db.get_domain_stats(domain)
        assert domain_stats is not None
        assert domain_stats.total_crawls == 3
        assert domain_stats.successful_crawls == 2
        assert domain_stats.failed_crawls == 1
        assert domain_stats.unique_pages == 3
    
    def test_page_stats_tracking(self, temp_db):
        """Test that page statistics are properly tracked."""
        url = "https://example.com/test-page"
        
        # Record multiple visits to the same page
        for i in range(2):
            record = CrawlRecord(
                url=url,
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS,
                start_time=datetime.now(),
                content_length=1500
            )
            temp_db.record_crawl(record)
        
        # Check page statistics
        page_stats = temp_db.get_page_stats(url)
        assert page_stats is not None
        assert page_stats.visit_count == 2
        assert page_stats.successful_visits == 2
        assert page_stats.url == url
    
    def test_get_top_domains(self, temp_db):
        """Test retrieving top domains by crawl count."""
        # Record crawls for different domains
        domains = ["example.com", "test.com", "demo.com"]
        crawl_counts = [5, 3, 7]
        
        for domain, count in zip(domains, crawl_counts):
            for i in range(count):
                record = CrawlRecord(
                    url=f"https://{domain}/page{i}",
                    crawl_type=CrawlType.WEBSITE,
                    status=CrawlStatus.SUCCESS,
                    start_time=datetime.now()
                )
                temp_db.record_crawl(record)
        
        # Get top domains
        top_domains = temp_db.get_top_domains(limit=3)
        assert len(top_domains) == 3
        
        # Should be ordered by crawl count (descending)
        assert top_domains[0].domain == "demo.com"  # 7 crawls
        assert top_domains[1].domain == "example.com"  # 5 crawls
        assert top_domains[2].domain == "test.com"  # 3 crawls
    
    def test_crawl_history_filtering(self, temp_db):
        """Test crawl history retrieval with filtering."""
        # Record crawls with different statuses and times
        base_time = datetime.now()
        
        records = [
            CrawlRecord(
                url="https://example.com/success",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS,
                start_time=base_time
            ),
            CrawlRecord(
                url="https://example.com/failed",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.FAILED,
                start_time=base_time + timedelta(minutes=1)
            ),
            CrawlRecord(
                url="https://other.com/success",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS,
                start_time=base_time + timedelta(minutes=2)
            )
        ]
        
        for record in records:
            temp_db.record_crawl(record)
        
        # Test filtering by domain
        example_crawls = temp_db.get_crawl_history(domain="example.com")
        assert len(example_crawls) == 2
        
        # Test filtering by status
        failed_crawls = temp_db.get_crawl_history(status=CrawlStatus.FAILED)
        assert len(failed_crawls) == 1
        assert failed_crawls[0].status == CrawlStatus.FAILED
    
    def test_summary_stats(self, temp_db):
        """Test summary statistics calculation."""
        # Record some test data
        for i in range(10):
            status = CrawlStatus.SUCCESS if i < 8 else CrawlStatus.FAILED
            record = CrawlRecord(
                url=f"https://example{i % 3}.com/page",
                crawl_type=CrawlType.WEBSITE,
                status=status,
                start_time=datetime.now(),
                duration_seconds=1.0 + i * 0.1,
                content_length=1000 + i * 100
            )
            temp_db.record_crawl(record)
        
        # Get summary stats
        stats = temp_db.get_summary_stats()
        
        assert stats['total_crawls'] == 10
        assert stats['successful_crawls'] == 8
        assert stats['failed_crawls'] == 2
        assert stats['success_rate'] == 80.0
        assert stats['total_domains'] == 3  # example0, example1, example2
        assert stats['avg_duration'] > 0


class TestStatisticsTracker:
    """Test StatisticsTracker functionality."""
    
    @pytest.fixture
    def temp_tracker(self):
        """Create a tracker with temporary database."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        # Create tracker with temporary database
        tracker = StatisticsTracker()
        tracker._database = StatisticsDatabase(db_path)
        
        yield tracker
        
        # Cleanup
        tracker._database.close()
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    def test_track_crawl_operation(self, temp_tracker):
        """Test tracking a crawl operation."""
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=2)
        
        record_id = temp_tracker.track_crawl_operation(
            url="https://example.com/test",
            crawl_type=CrawlType.WEBSITE,
            start_time=start_time,
            end_time=end_time,
            status=CrawlStatus.SUCCESS,
            content_length=1024,
            response_code=200
        )
        
        assert record_id > 0
        
        # Verify the record was stored
        record = temp_tracker.database.get_crawl_record(record_id)
        assert record is not None
        assert record.url == "https://example.com/test"
        assert record.status == CrawlStatus.SUCCESS


class TestStatisticsAnalyzer:
    """Test StatisticsAnalyzer functionality."""
    
    @pytest.fixture
    def analyzer_with_data(self):
        """Create analyzer with sample data."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        db = StatisticsDatabase(db_path)
        analyzer = StatisticsAnalyzer(db)
        
        # Add sample data
        base_time = datetime.now() - timedelta(days=7)
        for i in range(20):
            record = CrawlRecord(
                url=f"https://example{i % 3}.com/page{i}",
                crawl_type=CrawlType.WEBSITE,
                status=CrawlStatus.SUCCESS if i % 5 != 0 else CrawlStatus.FAILED,
                start_time=base_time + timedelta(hours=i),
                duration_seconds=1.0 + (i % 5) * 0.5,
                content_length=1000 + i * 50
            )
            db.record_crawl(record)
        
        yield analyzer
        
        # Cleanup
        db.close()
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    def test_comprehensive_report_generation(self, analyzer_with_data):
        """Test generating a comprehensive statistics report."""
        report = analyzer_with_data.generate_comprehensive_report(top_n=5)
        
        assert report.report_type == "comprehensive"
        assert report.total_crawls == 20
        assert report.total_domains == 3
        assert report.success_rate == 80.0  # 16 success, 4 failed
        assert len(report.top_domains) <= 5
        assert len(report.insights) > 0
    
    def test_domain_performance_analysis(self, analyzer_with_data):
        """Test domain performance analysis."""
        analysis = analyzer_with_data.analyze_domain_performance("example0.com")
        
        assert analysis["domain"] == "example0.com"
        assert "total_crawls" in analysis
        assert "success_rate" in analysis
        assert "performance_trend" in analysis


class TestStatisticsTools:
    """Test LangGraph statistics tools."""
    
    @patch('crawler.statistics.tools.StatisticsAnalyzer')
    def test_get_crawl_statistics_summary_tool(self, mock_analyzer_class):
        """Test the statistics summary tool."""
        # Mock the analyzer
        mock_analyzer = MagicMock()
        mock_analyzer_class.return_value = mock_analyzer
        
        # Mock report
        mock_report = MagicMock()
        mock_report.report_type = "comprehensive"
        mock_report.total_crawls = 100
        mock_report.success_rate = 95.0
        mock_report.top_domains = []
        mock_report.top_pages = []
        mock_report.common_errors = {}
        mock_report.insights = ["Test insight"]
        mock_report.generated_at = datetime.now()
        mock_report.avg_crawl_duration = 2.5
        mock_report.total_content_crawled = 50000
        
        mock_analyzer.generate_comprehensive_report.return_value = mock_report
        
        # Test the tool
        input_data = StatisticsReportInput(days_back=30, top_n=10)
        result = get_crawl_statistics_summary(input_data)
        
        assert "error" not in result
        assert result["summary"]["total_crawls"] == 100
        assert result["summary"]["success_rate"] == 95.0
        assert len(result["insights"]) == 1
    
    @patch('crawler.statistics.tools.generate_domain_report')
    def test_analyze_domain_performance_tool(self, mock_generate_report):
        """Test the domain analysis tool."""
        # Mock the domain report
        mock_report = {
            "domain": "example.com",
            "total_crawls": 50,
            "success_rate": 90.0,
            "unique_pages": 25,
            "avg_duration": 1.8,
            "first_crawl": datetime.now() - timedelta(days=30),
            "last_crawl": datetime.now(),
            "performance_trend": {"trend": "improving"},
            "error_analysis": {"error_rate": 10.0},
            "peak_hours": {"most_active_hour": 14},
            "crawl_frequency": {"avg_interval_hours": 2.5}
        }
        
        mock_generate_report.return_value = mock_report
        
        # Test the tool
        input_data = DomainAnalysisInput(domain="example.com")
        result = analyze_domain_performance(input_data)
        
        assert "error" not in result
        assert result["domain"] == "example.com"
        assert result["overview"]["total_crawls"] == 50
        assert result["overview"]["success_rate"] == 90.0
