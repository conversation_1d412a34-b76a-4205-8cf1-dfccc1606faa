"""
Tests for the historical data importer functionality.

This module tests the HistoricalDataImporter class and related tools
for importing historical crawl data from existing files and logs.
"""

import pytest
import tempfile
import json
from datetime import datetime
from pathlib import Path
from unittest.mock import patch, MagicMock

from crawler.statistics.historical_importer import HistoricalDataImporter
from crawler.statistics.models import CrawlRecord
from crawler.statistics.tools import preview_historical_import, import_historical_data


class TestHistoricalDataImporter:
    """Test cases for HistoricalDataImporter class."""

    @pytest.fixture
    def temp_project_dir(self):
        """Create a temporary project directory with test data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)

            # Create directory structure
            (project_root / 'results' / 'example.com').mkdir(parents=True)
            (project_root / 'scraped_html' / 'example.com').mkdir(parents=True)
            (project_root / 'screenshots' / 'example.com').mkdir(parents=True)
            (project_root / 'pdfs' / 'example.com').mkdir(parents=True)
            (project_root / 'logs').mkdir(parents=True)

            # Create test files with timestamps
            test_files = [
                # Results
                project_root / 'results' / 'example.com' / '20250707_194834.json',
                project_root / 'results' / 'example.com' / 'summary_20250707_194729.md',

                # HTML files
                project_root / 'scraped_html' / 'example.com' / 'page_20250707_194834.html',

                # Screenshots
                project_root / 'screenshots' / 'example.com' / 'screenshot_20250707_194834.png',

                # PDFs
                project_root / 'pdfs' / 'example.com' / 'document_20250707_194834.pdf',

                # Log files
                project_root / 'logs' / 'agent_20250707_194834.log',
            ]

            # Create test content
            for file_path in test_files:
                if file_path.suffix == '.json':
                    content = {
                        "url": "https://example.com",
                        "title": "Test Page",
                        "content": "Test content",
                        "timestamp": "2025-07-07T19:48:34"
                    }
                    file_path.write_text(json.dumps(content, indent=2))
                elif file_path.suffix == '.log':
                    content = """2025-07-07 19:48:34 - INFO - Processing request: summarize https://example.com
2025-07-07 19:48:35 - INFO - Crawling URL: https://example.com
2025-07-07 19:48:36 - INFO - Successfully crawled: https://example.com
"""
                    file_path.write_text(content)
                else:
                    # Create dummy content for other file types
                    file_path.write_text("Test content")

            yield project_root

    def test_importer_initialization(self, temp_project_dir):
        """Test HistoricalDataImporter initialization."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        assert importer.project_root == temp_project_dir
        assert 'results' in importer.directories
        assert 'scraped_html' in importer.directories
        assert 'screenshots' in importer.directories
        assert 'pdfs' in importer.directories
        assert 'logs' in importer.directories

    def test_get_import_preview(self, temp_project_dir):
        """Test import preview generation."""
        importer = HistoricalDataImporter(str(temp_project_dir))
        preview = importer.get_import_preview()

        # Check basic structure
        assert 'directories_found' in preview
        assert 'estimated_records' in preview
        assert 'domains_found' in preview
        assert 'file_types' in preview
        assert 'date_range' in preview

        # Check that data was found
        assert preview['estimated_records'] > 0
        assert len(preview['domains_found']) > 0
        assert 'example.com' in preview['domains_found']
        assert len(preview['file_types']) > 0

        # Check file types (logs are processed separately, so may not appear in file_types)
        expected_types = {'.json', '.md', '.html', '.png', '.pdf'}
        found_types = set(preview['file_types'].keys())
        assert expected_types.issubset(found_types)

    def test_extract_timestamp_from_file(self, temp_project_dir):
        """Test timestamp extraction from filenames."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        # Test different timestamp formats
        test_cases = [
            ('file_20250707_194834.json', datetime(2025, 7, 7, 19, 48, 34)),
            ('document_2025-07-07.pdf', datetime(2025, 7, 7, 0, 0, 0)),
            # Note: The datetime format pattern only captures date, not time
        ]

        for filename, expected_timestamp in test_cases:
            file_path = temp_project_dir / filename
            file_path.touch()  # Create the file

            # Method now expects string input due to caching optimization
            timestamp = importer._extract_timestamp_from_file(str(file_path))
            assert timestamp == expected_timestamp

    def test_parse_log_file(self, temp_project_dir):
        """Test log file parsing for crawl records."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        log_file = temp_project_dir / 'logs' / 'agent_20250707_194834.log'
        base_timestamp = datetime(2025, 7, 7, 19, 48, 34)

        records = importer._parse_log_file(log_file, base_timestamp)

        assert len(records) > 0

        # Check that records were created
        for record in records:
            assert isinstance(record, CrawlRecord)
            assert record.url is not None
            assert record.domain is not None
            assert record.start_time == base_timestamp

    def test_create_crawl_record_from_file(self, temp_project_dir):
        """Test creating crawl records from file information."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        file_path = temp_project_dir / 'results' / 'example.com' / 'test.json'
        file_path.write_text('{"test": "data"}')

        timestamp = datetime.now()
        record = importer._create_crawl_record_from_file(
            'example.com', file_path, timestamp, 'results'
        )

        assert record is not None
        assert record.domain == 'example.com'
        assert record.url == 'https://example.com/'
        assert record.start_time == timestamp
        assert record.status == 'success'
        assert record.crawl_type == 'website'

    @patch('crawler.statistics.historical_importer.StatisticsDatabase')
    def test_import_all_historical_data_dry_run(self, mock_db, temp_project_dir):
        """Test dry run import (no actual database operations)."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        results = importer.import_all_historical_data(dry_run=True)

        # Check results structure
        assert 'logs_processed' in results
        assert 'crawl_records_imported' in results
        assert 'domains_discovered' in results
        assert 'errors' in results

        # In dry run, no database operations should occur
        mock_db.return_value.record_crawl.assert_not_called()

    @patch('crawler.statistics.historical_importer.StatisticsDatabase')
    def test_import_all_historical_data_actual(self, mock_db, temp_project_dir):
        """Test actual import with database operations."""
        mock_db_instance = MagicMock()
        mock_db.return_value = mock_db_instance

        importer = HistoricalDataImporter(str(temp_project_dir))
        results = importer.import_all_historical_data(dry_run=False)

        # Check that database operations were called
        assert mock_db_instance.record_crawl.call_count > 0

        # Check results
        assert results['crawl_records_imported'] > 0
        assert results['domains_discovered'] > 0

    def test_extract_url_from_log_entry(self, temp_project_dir):
        """Test URL extraction from log entries."""
        importer = HistoricalDataImporter(str(temp_project_dir))

        test_cases = [
            ("Processing request: summarize https://example.com", "https://example.com"),
            ("Crawling URL: www.test.com", "https://www.test.com"),
            ("Successfully crawled: example.org", "https://example.org"),
            ("Error crawling https://broken.com: timeout", "https://broken.com"),
            ("No URL in this log entry", None),
        ]

        for log_entry, expected_url in test_cases:
            url = importer._extract_url_from_log_entry(log_entry)
            assert url == expected_url


class TestHistoricalImportTools:
    """Test cases for LangChain tools."""

    @pytest.fixture
    def temp_project_dir(self):
        """Create a temporary project directory with test data."""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)

            # Create minimal test structure
            (project_root / 'results' / 'test.com').mkdir(parents=True)
            (project_root / 'logs').mkdir(parents=True)

            # Create test files
            (project_root / 'results' / 'test.com' /
             'test.json').write_text('{"test": "data"}')
            (project_root / 'logs' / 'agent_20250707_194834.log').write_text(
                "2025-07-07 19:48:34 - INFO - Crawling URL: https://test.com\n"
            )

            yield str(project_root)

    def test_preview_historical_import_tool(self, temp_project_dir):
        """Test the preview_historical_import LangChain tool."""
        input_data = {
            "input_data": {
                "dry_run": True,
                "project_root": temp_project_dir
            }
        }

        result = preview_historical_import.invoke(input_data)

        assert result['status'] == 'success'
        assert 'preview' in result
        assert 'message' in result

        preview = result['preview']
        assert 'summary' in preview
        assert preview['summary']['estimated_records'] > 0
        assert preview['summary']['domains_found'] > 0

    def test_import_historical_data_tool_dry_run(self, temp_project_dir):
        """Test the import_historical_data LangChain tool in dry run mode."""
        input_data = {
            "input_data": {
                "dry_run": True,
                "project_root": temp_project_dir
            }
        }

        result = import_historical_data.invoke(input_data)

        assert result['status'] == 'success'
        assert 'results' in result
        assert 'message' in result

        results = result['results']
        assert 'crawl_records_imported' in results
        assert 'logs_processed' in results
        assert 'domains_discovered' in results

    @patch('crawler.statistics.tools.StatisticsDatabase')
    def test_import_historical_data_tool_actual(self, mock_db, temp_project_dir):
        """Test the import_historical_data LangChain tool with actual import."""
        input_data = {
            "input_data": {
                "dry_run": False,
                "project_root": temp_project_dir
            }
        }

        result = import_historical_data.invoke(input_data)

        assert result['status'] == 'success'
        assert 'results' in result

    def test_preview_tool_error_handling(self):
        """Test error handling in preview tool."""
        input_data = {
            "input_data": {
                "dry_run": True,
                "project_root": "/nonexistent/path"
            }
        }

        result = preview_historical_import.invoke(input_data)

        # Should handle errors gracefully
        assert 'error' in result or result.get('status') == 'success'

    def test_import_tool_error_handling(self):
        """Test error handling in import tool."""
        input_data = {
            "input_data": {
                "dry_run": True,
                "project_root": "/nonexistent/path"
            }
        }

        result = import_historical_data.invoke(input_data)

        # Should handle errors gracefully
        assert 'error' in result or result.get('status') == 'success'


class TestTimestampExtraction:
    """Test cases for timestamp extraction functionality."""

    def test_timestamp_patterns(self):
        """Test various timestamp pattern matching."""
        importer = HistoricalDataImporter()

        test_cases = [
            # Filename patterns - test that at least one pattern matches
            ('agent_20250707_194834.log', True),
            ('file_20250707_194834.json', True),
            ('document_2025-07-07.pdf', True),
            ('page_2025-07-07_19-48-34.html', True),
            ('no_timestamp_file.txt', False),
        ]

        for filename, should_match in test_cases:
            found_match = False
            for pattern in importer.timestamp_patterns.values():
                import re
                match = re.search(pattern, filename)
                if match:
                    found_match = True
                    break

            if should_match:
                assert found_match, f"Expected pattern match for filename: {filename}"
            else:
                assert not found_match, f"Unexpected pattern match for filename: {filename}"

    def test_date_range_update(self):
        """Test date range tracking."""
        importer = HistoricalDataImporter()

        date_range = {'earliest': None, 'latest': None}

        # Add timestamps
        timestamps = [
            datetime(2025, 7, 5, 10, 0, 0),
            datetime(2025, 7, 7, 15, 30, 0),
            datetime(2025, 7, 3, 8, 15, 0),
            datetime(2025, 7, 10, 20, 45, 0),
        ]

        for timestamp in timestamps:
            importer._update_date_range(date_range, timestamp)

        assert date_range['earliest'] == datetime(2025, 7, 3, 8, 15, 0)
        assert date_range['latest'] == datetime(2025, 7, 10, 20, 45, 0)


if __name__ == '__main__':
    pytest.main([__file__])
