"""
Comprehensive tests for schema learning crawler functionality.

This module tests the SchemaLearner class and smart_crawler function,
including schema caching, learning, error handling, and integration scenarios.
"""

import pytest
import asyncio
import os
import json
import tempfile
import shutil
from unittest.mock import patch, MagicMock, AsyncMock
from typing import Dict, Any
from pydantic import BaseModel, Field

# Import the schema learning crawler components
from crawler import SchemaLearner, smart_crawler


class TestDataModel(BaseModel):
    """Test model for schema learning tests."""
    name: str = Field(..., description="Name field")
    value: str = Field(..., description="Value field")
    price: str = Field(..., description="Price field")


class TestSchemaLearner:
    """Test cases for SchemaLearner class."""

    def setup_method(self):
        """Set up test environment before each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.schema_learner = SchemaLearner(schema_dir=self.temp_dir)
        self.test_url = "https://example.com"
        self.test_schema = {
            "name": "test_extractor",
            "baseSelector": ".item",
            "fields": [
                {"name": "name", "selector": ".name", "type": "text"},
                {"name": "value", "selector": ".value", "type": "text"},
                {"name": "price", "selector": ".price", "type": "text"}
            ]
        }

    def teardown_method(self):
        """Clean up test environment after each test."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_init_creates_schema_directory(self):
        """Test that SchemaLearner creates schema directory on initialization."""
        new_temp_dir = os.path.join(self.temp_dir, "new_schemas")
        learner = SchemaLearner(schema_dir=new_temp_dir)
        assert os.path.exists(new_temp_dir)

    def test_get_schema_path(self):
        """Test schema path generation from URL."""
        url = "https://example.com/page"
        expected_path = os.path.join(self.temp_dir, "example.com_schema.json")
        assert self.schema_learner.get_schema_path(url) == expected_path

        # Test with different URL formats
        url_http = "http://test.org/path"
        expected_path_http = os.path.join(
            self.temp_dir, "test.org_schema.json")
        assert self.schema_learner.get_schema_path(
            url_http) == expected_path_http

    def test_save_and_load_schema(self):
        """Test saving and loading schema functionality."""
        # Save schema
        self.schema_learner.save_schema(self.test_url, self.test_schema)

        # Verify file exists
        schema_path = self.schema_learner.get_schema_path(self.test_url)
        assert os.path.exists(schema_path)

        # Load schema
        loaded_schema = self.schema_learner.load_schema(self.test_url)
        assert loaded_schema == self.test_schema

    def test_load_nonexistent_schema(self):
        """Test loading schema that doesn't exist returns None."""
        result = self.schema_learner.load_schema("https://nonexistent.com")
        assert result is None

    def test_save_schema_creates_valid_json(self):
        """Test that saved schema is valid JSON."""
        self.schema_learner.save_schema(self.test_url, self.test_schema)
        schema_path = self.schema_learner.get_schema_path(self.test_url)

        with open(schema_path, 'r') as f:
            loaded_data = json.load(f)

        assert loaded_data == self.test_schema


class TestSmartCrawler:
    """Test cases for smart_crawler function."""

    def setup_method(self):
        """Set up test environment before each test."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_url = "https://example.com"
        self.test_model = TestDataModel
        self.mock_schema = {
            "name": "test_extractor",
            "baseSelector": ".item",
            "fields": [
                {"name": "name", "selector": ".name", "type": "text"},
                {"name": "value", "selector": ".value", "type": "text"},
                {"name": "price", "selector": ".price", "type": "text"}
            ]
        }
        self.mock_extracted_data = [
            {"name": "Item 1", "value": "Value 1", "price": "$10"},
            {"name": "Item 2", "value": "Value 2", "price": "$20"}
        ]

    def teardown_method(self):
        """Clean up test environment after each test."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_with_existing_schema(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler when schema already exists."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = self.mock_schema
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.extracted_content = json.dumps(self.mock_extracted_data)
        mock_crawler.arun.return_value = mock_result
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler
        result = await smart_crawler(self.test_url, self.test_model)

        # Verify behavior
        mock_learner.load_schema.assert_called_once_with(self.test_url)
        # Should not save when schema exists
        mock_learner.save_schema.assert_not_called()
        assert result == self.mock_extracted_data

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_learns_new_schema(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler when no schema exists and needs to learn."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = None  # No existing schema
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        # First call (learning): return schema
        # Second call (extraction): return data
        mock_results = [
            MagicMock(success=True, extracted_content=json.dumps(
                self.mock_schema)),
            MagicMock(success=True, extracted_content=json.dumps(
                self.mock_extracted_data))
        ]
        mock_crawler.arun.side_effect = mock_results
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler
        result = await smart_crawler(self.test_url, self.test_model)

        # Verify behavior
        mock_learner.load_schema.assert_called_once_with(self.test_url)
        mock_learner.save_schema.assert_called_once_with(
            self.test_url, self.mock_schema)
        assert mock_crawler.arun.call_count == 2  # Called twice: learn + extract
        assert result == self.mock_extracted_data

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_force_learn(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler with force_learn=True."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = self.mock_schema  # Existing schema
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        mock_results = [
            MagicMock(success=True, extracted_content=json.dumps(
                self.mock_schema)),
            MagicMock(success=True, extracted_content=json.dumps(
                self.mock_extracted_data))
        ]
        mock_crawler.arun.side_effect = mock_results
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler with force_learn=True
        result = await smart_crawler(self.test_url, self.test_model, force_learn=True)

        # Verify behavior
        # Should not load when force_learn=True
        mock_learner.load_schema.assert_not_called()
        mock_learner.save_schema.assert_called_once_with(
            self.test_url, self.mock_schema)
        assert mock_crawler.arun.call_count == 2  # Called twice: learn + extract
        assert result == self.mock_extracted_data

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_learning_failure(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler when schema learning fails."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = None
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = False
        mock_result.error_message = "Learning failed"
        mock_crawler.arun.return_value = mock_result
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler and expect exception
        with pytest.raises(Exception, match="Failed to learn schema"):
            await smart_crawler(self.test_url, self.test_model)

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_extraction_failure(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler when data extraction fails."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = self.mock_schema
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = False
        mock_result.error_message = "Extraction failed"
        mock_crawler.arun.return_value = mock_result
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler and expect exception
        with pytest.raises(Exception, match="Failed to extract data"):
            await smart_crawler(self.test_url, self.test_model)

    @pytest.mark.asyncio
    @patch('crawler.crawler.SchemaLearner')
    @patch('crawler.crawler.AsyncWebCrawler')
    async def test_smart_crawler_invalid_learned_schema(self, mock_crawler_class, mock_learner_class):
        """Test smart_crawler when learned schema is invalid JSON."""
        # Setup mocks
        mock_learner = MagicMock()
        mock_learner.load_schema.return_value = None
        mock_learner_class.return_value = mock_learner

        mock_crawler = AsyncMock()
        mock_result = MagicMock()
        mock_result.success = True
        mock_result.extracted_content = "invalid json"  # Invalid JSON
        mock_crawler.arun.return_value = mock_result
        mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

        # Run smart_crawler and expect exception
        with pytest.raises(Exception, match="Failed to parse learned schema"):
            await smart_crawler(self.test_url, self.test_model)


@pytest.mark.asyncio
class TestIntegration:
    """Integration tests for schema learning crawler."""

    def setup_method(self):
        """Set up test environment before each test."""
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """Clean up test environment after each test."""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @pytest.mark.asyncio
    async def test_schema_persistence_across_calls(self):
        """Test that schema is properly saved and reused across multiple calls."""
        with patch('crawler.crawler.AsyncWebCrawler') as mock_crawler_class:
            # Setup mock for first call (learning)
            mock_crawler = AsyncMock()
            mock_schema = {"name": "test", "fields": []}
            mock_data = [{"name": "test"}]

            mock_results = [
                MagicMock(success=True,
                          extracted_content=json.dumps(mock_schema)),
                MagicMock(success=True, extracted_content=json.dumps(mock_data)),
                MagicMock(success=True, extracted_content=json.dumps(
                    mock_data))  # Second call
            ]
            mock_crawler.arun.side_effect = mock_results
            mock_crawler_class.return_value.__aenter__.return_value = mock_crawler

            # First call - should learn schema
            with patch('crawler.crawler.SchemaLearner') as mock_learner_class:
                mock_learner = SchemaLearner(schema_dir=self.temp_dir)
                mock_learner_class.return_value = mock_learner

                result1 = await smart_crawler("https://test.com", TestDataModel)
                assert result1 == mock_data

                # Second call - should use cached schema
                result2 = await smart_crawler("https://test.com", TestDataModel)
                assert result2 == mock_data

                # Verify schema was saved and reused
                assert mock_crawler.arun.call_count == 3  # 2 for first call, 1 for second call


if __name__ == "__main__":
    pytest.main([__file__])
