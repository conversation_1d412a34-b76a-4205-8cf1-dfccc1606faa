"""
Tests for sitemap crawling functionality.

This module contains comprehensive tests for:
- Sitemap URL extraction from standard locations
- Robots.txt parsing for sitemap discovery
- XML parsing for both regular sitemaps and sitemap indexes
- Error handling and edge cases
- Batch processing of multiple sitemaps
"""

import pytest
import asyncio
import xml.etree.ElementTree as ET
from unittest.mock import AsyncMock, patch, MagicMock
from aiohttp import ClientResponse, ClientSession
import gzip

from crawler.sitemap import (
    read_sitemap,
    read_multiple_sitemaps,
    _find_sitemaps_in_robots,
    _extract_urls_from_sitemap,
    _download_sitemap_content,
    _is_sitemap_index,
    _extract_sitemap_urls_from_index,
    _extract_page_urls_from_sitemap
)


class TestSitemapReading:
    """Test cases for main sitemap reading functionality."""

    @pytest.mark.asyncio
    async def test_read_sitemap_standard_location_success(self):
        """Test successful sitemap reading from standard location."""
        mock_sitemap_xml = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url>
                <loc>https://example.com/page1</loc>
            </url>
            <url>
                <loc>https://example.com/page2</loc>
            </url>
        </urlset>"""

        with patch('crawler.sitemap._extract_urls_from_sitemap') as mock_extract:
            mock_extract.return_value = {
                'https://example.com/page1', 'https://example.com/page2'}

            result = await read_sitemap('https://example.com')

            assert result['source'] == 'https://example.com/sitemap.xml'
            assert len(result['urls']) == 2
            assert 'https://example.com/page1' in result['urls']
            assert 'https://example.com/page2' in result['urls']

    @pytest.mark.asyncio
    async def test_read_sitemap_fallback_to_robots(self):
        """Test fallback to robots.txt when standard location fails."""
        with patch('crawler.sitemap._extract_urls_from_sitemap') as mock_extract, \
                patch('crawler.sitemap._find_sitemaps_in_robots') as mock_robots:

            # First call (standard location) fails
            mock_extract.side_effect = [
                Exception("Not found"), {'https://example.com/page1'}]
            # Robots.txt returns sitemap URL
            mock_robots.return_value = [
                'https://example.com/custom-sitemap.xml']

            result = await read_sitemap('https://example.com')

            assert result['source'] == 'https://example.com/custom-sitemap.xml'
            assert len(result['urls']) == 1
            assert 'https://example.com/page1' in result['urls']

    @pytest.mark.asyncio
    async def test_read_sitemap_no_sitemap_found(self):
        """Test behavior when no sitemap is found."""
        with patch('crawler.sitemap._extract_urls_from_sitemap') as mock_extract, \
                patch('crawler.sitemap._find_sitemaps_in_robots') as mock_robots:

            mock_extract.side_effect = Exception("Not found")
            mock_robots.return_value = []

            result = await read_sitemap('https://example.com')

            assert result['source'] is None
            assert result['urls'] == []


class TestRobotsTextParsing:
    """Test cases for robots.txt parsing functionality."""

    @pytest.mark.asyncio
    async def test_find_sitemaps_in_robots_success(self):
        """Test successful sitemap discovery from robots.txt."""
        robots_content = """User-agent: *
Disallow: /admin/
Sitemap: https://example.com/sitemap.xml
Sitemap: https://example.com/news-sitemap.xml

# Another comment
Sitemap: https://example.com/images-sitemap.xml"""

        # Mock the entire aiohttp session context
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text = AsyncMock(return_value=robots_content)

            mock_session.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session.__aexit__ = AsyncMock(return_value=None)
            mock_session.get = AsyncMock()
            mock_session.get.return_value.__aenter__ = AsyncMock(
                return_value=mock_response)
            mock_session.get.return_value.__aexit__ = AsyncMock(
                return_value=None)

            mock_session_class.return_value = mock_session

            result = await _find_sitemaps_in_robots('https://example.com', 30)

            assert len(result) == 3
            assert 'https://example.com/sitemap.xml' in result
            assert 'https://example.com/news-sitemap.xml' in result
            assert 'https://example.com/images-sitemap.xml' in result

    @pytest.mark.asyncio
    async def test_find_sitemaps_in_robots_not_found(self):
        """Test robots.txt not found scenario."""
        mock_response = AsyncMock()
        mock_response.status = 404

        mock_session = AsyncMock()
        mock_session.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await _find_sitemaps_in_robots('https://example.com', 30)

            assert result == []

    @pytest.mark.asyncio
    async def test_find_sitemaps_in_robots_no_sitemaps(self):
        """Test robots.txt with no sitemap entries."""
        robots_content = """User-agent: *
Disallow: /admin/
Crawl-delay: 1"""

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = robots_content

        mock_session = AsyncMock()
        mock_session.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await _find_sitemaps_in_robots('https://example.com', 30)

            assert result == []


class TestXMLParsing:
    """Test cases for XML parsing functionality."""

    def test_is_sitemap_index_true(self):
        """Test identification of sitemap index."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <sitemap>
                <loc>https://example.com/sitemap1.xml</loc>
            </sitemap>
        </sitemapindex>"""

        root = ET.fromstring(xml_content)
        assert _is_sitemap_index(root) is True

    def test_is_sitemap_index_false(self):
        """Test identification of regular sitemap."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url>
                <loc>https://example.com/page1</loc>
            </url>
        </urlset>"""

        root = ET.fromstring(xml_content)
        assert _is_sitemap_index(root) is False

    def test_extract_sitemap_urls_from_index(self):
        """Test extraction of sitemap URLs from index."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <sitemap>
                <loc>https://example.com/sitemap1.xml</loc>
                <lastmod>2023-01-01</lastmod>
            </sitemap>
            <sitemap>
                <loc>https://example.com/sitemap2.xml</loc>
                <lastmod>2023-01-02</lastmod>
            </sitemap>
        </sitemapindex>"""

        root = ET.fromstring(xml_content)
        urls = _extract_sitemap_urls_from_index(root)

        assert len(urls) == 2
        assert 'https://example.com/sitemap1.xml' in urls
        assert 'https://example.com/sitemap2.xml' in urls

    def test_extract_page_urls_from_sitemap(self):
        """Test extraction of page URLs from regular sitemap."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url>
                <loc>https://example.com/page1</loc>
                <lastmod>2023-01-01</lastmod>
                <priority>1.0</priority>
            </url>
            <url>
                <loc>https://example.com/page2</loc>
                <lastmod>2023-01-02</lastmod>
                <priority>0.8</priority>
            </url>
        </urlset>"""

        root = ET.fromstring(xml_content)
        urls = _extract_page_urls_from_sitemap(root)

        assert len(urls) == 2
        assert 'https://example.com/page1' in urls
        assert 'https://example.com/page2' in urls


class TestContentDownload:
    """Test cases for sitemap content download functionality."""

    @pytest.mark.asyncio
    async def test_download_sitemap_content_success(self):
        """Test successful sitemap content download."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url><loc>https://example.com/page1</loc></url>
        </urlset>"""

        # Mock the entire aiohttp session context
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.read = AsyncMock(
                return_value=xml_content.encode('utf-8'))
            mock_response.headers = {}

            mock_session.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session.__aexit__ = AsyncMock(return_value=None)
            mock_session.get = AsyncMock()
            mock_session.get.return_value.__aenter__ = AsyncMock(
                return_value=mock_response)
            mock_session.get.return_value.__aexit__ = AsyncMock(
                return_value=None)

            mock_session_class.return_value = mock_session

            result = await _download_sitemap_content('https://example.com/sitemap.xml', 30)

            assert result == xml_content

    @pytest.mark.asyncio
    async def test_download_sitemap_content_gzipped(self):
        """Test download of gzipped sitemap content."""
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
            <url><loc>https://example.com/page1</loc></url>
        </urlset>"""

        gzipped_content = gzip.compress(xml_content.encode('utf-8'))

        # Mock the entire aiohttp session context
        with patch('aiohttp.ClientSession') as mock_session_class:
            mock_session = AsyncMock()
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.read = AsyncMock(return_value=gzipped_content)
            mock_response.headers = {'content-encoding': 'gzip'}

            mock_session.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session.__aexit__ = AsyncMock(return_value=None)
            mock_session.get = AsyncMock()
            mock_session.get.return_value.__aenter__ = AsyncMock(
                return_value=mock_response)
            mock_session.get.return_value.__aexit__ = AsyncMock(
                return_value=None)

            mock_session_class.return_value = mock_session

            result = await _download_sitemap_content('https://example.com/sitemap.xml.gz', 30)

            assert result == xml_content

    @pytest.mark.asyncio
    async def test_download_sitemap_content_404(self):
        """Test handling of 404 response."""
        mock_response = AsyncMock()
        mock_response.status = 404

        mock_session = AsyncMock()
        mock_session.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response

        with patch('aiohttp.ClientSession', return_value=mock_session):
            result = await _download_sitemap_content('https://example.com/sitemap.xml', 30)

            assert result is None


class TestBatchProcessing:
    """Test cases for batch sitemap processing."""

    @pytest.mark.asyncio
    async def test_read_multiple_sitemaps_success(self):
        """Test successful batch processing of multiple sitemaps."""
        urls = ['https://example1.com', 'https://example2.com']

        with patch('crawler.sitemap.read_sitemap') as mock_read:
            mock_read.side_effect = [
                {'source': 'https://example1.com/sitemap.xml',
                    'urls': ['https://example1.com/page1']},
                {'source': 'https://example2.com/sitemap.xml',
                    'urls': ['https://example2.com/page1', 'https://example2.com/page2']}
            ]

            results = await read_multiple_sitemaps(urls)

            assert len(results) == 2
            assert results[0]['success'] is True
            assert results[0]['base_url'] == 'https://example1.com'
            assert len(results[0]['urls']) == 1
            assert results[1]['success'] is True
            assert results[1]['base_url'] == 'https://example2.com'
            assert len(results[1]['urls']) == 2

    @pytest.mark.asyncio
    async def test_read_multiple_sitemaps_with_failures(self):
        """Test batch processing with some failures."""
        urls = ['https://example1.com', 'https://example2.com']

        with patch('crawler.sitemap.read_sitemap') as mock_read:
            mock_read.side_effect = [
                {'source': 'https://example1.com/sitemap.xml',
                    'urls': ['https://example1.com/page1']},
                Exception("Network error")
            ]

            results = await read_multiple_sitemaps(urls)

            assert len(results) == 2
            assert results[0]['success'] is True
            assert results[1]['success'] is False
            assert results[1]['error'] == "Network error"
