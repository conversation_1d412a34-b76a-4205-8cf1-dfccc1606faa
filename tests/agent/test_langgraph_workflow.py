"""
Tests for the LangGraph workflow components.
"""

import unittest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
import asyncio

from agent.langgraph_state import create_initial_state, WorkflowCommands
from agent.langgraph_workflow import CrawlAgent


class TestLangGraphWorkflow(unittest.IsolatedAsyncioTestCase):
    """Test cases for the LangGraph workflow."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.agent = None
    
    def tearDown(self):
        """Clean up after tests."""
        if self.agent:
            del self.agent
    
    def test_create_initial_state(self):
        """Test creating initial state."""
        user_prompt = "crawl example.com"
        state = create_initial_state(user_prompt)
        
        self.assertEqual(state["user_prompt"], user_prompt)
        self.assertIsNone(state["parsed_intent"])
        self.assertIsNone(state["url"])
        self.assertFalse(state["screenshot"])
        self.assertFalse(state["pdf"])
        self.assertEqual(state["messages"], [])
    
    @patch('agent.langgraph_workflow.create_workflow')
    def test_agent_initialization(self, mock_create_workflow):
        """Test agent initialization."""
        mock_workflow = MagicMock()
        mock_create_workflow.return_value = mock_workflow
        
        agent = CrawlAgent()
        
        self.assertIsNotNone(agent.workflow)
        self.assertIsNotNone(agent.app)
        mock_create_workflow.assert_called_once()
    
    async def test_process_request_empty_input(self):
        """Test processing empty request."""
        with patch('agent.langgraph_workflow.create_workflow') as mock_create_workflow:
            mock_workflow = MagicMock()
            mock_create_workflow.return_value = mock_workflow
            
            agent = CrawlAgent()
            
            # Test empty string
            result = await agent.process_request("")
            self.assertIn("error", result)
            self.assertIn("Empty request", result["error"])
            
            # Test whitespace only
            result = await agent.process_request("   ")
            self.assertIn("error", result)
            self.assertIn("Empty request", result["error"])
    
    async def test_process_request_timeout(self):
        """Test request timeout handling."""
        with patch('agent.langgraph_workflow.create_workflow') as mock_create_workflow:
            mock_workflow = MagicMock()
            mock_app = AsyncMock()
            
            # Make ainvoke hang to trigger timeout
            async def slow_invoke(*args, **kwargs):
                await asyncio.sleep(10)  # Longer than timeout
                return {}
            
            mock_app.ainvoke = slow_invoke
            mock_workflow.compile.return_value = mock_app
            mock_create_workflow.return_value = mock_workflow
            
            agent = CrawlAgent()
            
            # This should timeout quickly for testing
            with patch('agent.langgraph_workflow.asyncio.wait_for') as mock_wait_for:
                mock_wait_for.side_effect = asyncio.TimeoutError()
                
                result = await agent.process_request("test request")
                self.assertIn("error", result)
                self.assertIn("timed out", result["error"])
    
    async def test_process_request_success(self):
        """Test successful request processing."""
        with patch('agent.langgraph_workflow.create_workflow') as mock_create_workflow:
            mock_workflow = MagicMock()
            mock_app = AsyncMock()
            
            # Mock successful response
            mock_final_state = {
                "result": {"summary": "Test summary", "success": True}
            }
            mock_app.ainvoke = AsyncMock(return_value=mock_final_state)
            mock_workflow.compile.return_value = mock_app
            mock_create_workflow.return_value = mock_workflow
            
            agent = CrawlAgent()
            
            result = await agent.process_request("summarize example.com")
            
            self.assertNotIn("error", result)
            self.assertIn("summary", result)
            self.assertEqual(result["summary"], "Test summary")
    
    async def test_process_request_workflow_error(self):
        """Test handling workflow errors."""
        with patch('agent.langgraph_workflow.create_workflow') as mock_create_workflow:
            mock_workflow = MagicMock()
            mock_app = AsyncMock()
            
            # Mock error response
            mock_final_state = {
                "error": "Test workflow error"
            }
            mock_app.ainvoke = AsyncMock(return_value=mock_final_state)
            mock_workflow.compile.return_value = mock_app
            mock_create_workflow.return_value = mock_workflow
            
            agent = CrawlAgent()
            
            result = await agent.process_request("invalid request")
            
            self.assertIn("error", result)
            self.assertEqual(result["error"], "Test workflow error")
    
    async def test_process_request_exception(self):
        """Test handling unexpected exceptions."""
        with patch('agent.langgraph_workflow.create_workflow') as mock_create_workflow:
            mock_workflow = MagicMock()
            mock_app = AsyncMock()
            
            # Mock exception
            mock_app.ainvoke = AsyncMock(side_effect=Exception("Test exception"))
            mock_workflow.compile.return_value = mock_app
            mock_create_workflow.return_value = mock_workflow
            
            agent = CrawlAgent()
            
            result = await agent.process_request("test request")
            
            self.assertIn("error", result)
            self.assertIn("Failed to process request", result["error"])
            self.assertIn("Test exception", result["error"])


class TestWorkflowCommands(unittest.TestCase):
    """Test workflow command constants."""
    
    def test_workflow_commands_exist(self):
        """Test that all expected workflow commands exist."""
        expected_commands = [
            "CRAWL_WEBSITE", "CRAWL_IMPRESSUM", "PROCESS_LOCAL_FILE",
            "EXTRACT_LEGAL_INFO", "SUMMARIZE", "CHAT",
            "PARSE_INTENT", "VALIDATE_INPUT", "CRAWL_URL",
            "FIND_IMPRESSUM", "EXTRACT_DATA", "GENERATE_SUMMARY",
            "SAVE_RESULTS", "FORMAT_OUTPUT"
        ]
        
        for command in expected_commands:
            self.assertTrue(hasattr(WorkflowCommands, command))
            self.assertIsInstance(getattr(WorkflowCommands, command), str)
    
    def test_node_names_exist(self):
        """Test that all expected node names exist."""
        expected_nodes = [
            "INTENT_PARSER", "INPUT_VALIDATOR", "URL_CRAWLER",
            "LOCAL_FILE_PROCESSOR", "IMPRESSUM_FINDER", "DATA_EXTRACTOR",
            "SUMMARIZER", "RESULT_FORMATTER", "CHAT_RESPONDER"
        ]
        
        for node in expected_nodes:
            self.assertTrue(hasattr(WorkflowCommands, node))
            self.assertIsInstance(getattr(WorkflowCommands, node), str)


if __name__ == "__main__":
    unittest.main()
