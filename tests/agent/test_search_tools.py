"""
Tests for the search tools functionality.
"""


from agent.langgraph_tools import web_search_tool, search_and_crawl_tool
import asyncio
import sys
import os
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__)))))


class TestSearchTools(unittest.IsolatedAsyncioTestCase):
    """Test cases for search tools."""

    @patch('agent.langgraph_tools.DDGS')
    def test_web_search_tool_success(self, mock_ddgs):
        """Test successful web search."""
        # Mock search results
        mock_results = [
            {
                "title": "Test Result 1",
                "href": "https://example1.com",
                "body": "This is a test result description",
                "source": "example1.com"
            },
            {
                "title": "Test Result 2",
                "href": "https://example2.com",
                "body": "Another test result description",
                "source": "example2.com"
            }
        ]

        # Configure mock
        mock_ddgs_instance = MagicMock()
        mock_ddgs_instance.text.return_value = mock_results
        mock_ddgs.return_value.__enter__.return_value = mock_ddgs_instance

        # Test the search
        result = web_search_tool.invoke({
            "query": "test search",
            "max_results": 10
        })

        # Verify results
        self.assertIn("success", result)
        self.assertTrue(result["success"])
        self.assertIn("results", result)
        self.assertEqual(len(result["results"]), 2)
        self.assertEqual(result["results"][0]["title"], "Test Result 1")
        self.assertEqual(result["results"][0]["url"], "https://example1.com")
        self.assertEqual(result["query"], "test search")

    @patch('agent.langgraph_tools.DDGS')
    def test_web_search_tool_no_results(self, mock_ddgs):
        """Test web search with no results."""
        # Configure mock to return empty results
        mock_ddgs_instance = MagicMock()
        mock_ddgs_instance.text.return_value = []
        mock_ddgs.return_value.__enter__.return_value = mock_ddgs_instance

        # Test the search
        result = web_search_tool.invoke({
            "query": "nonexistent search term",
            "max_results": 10
        })

        # Verify error handling
        self.assertIn("error", result)
        self.assertIn("No search results found", result["error"])

    @patch('agent.langgraph_tools.DDGS')
    def test_web_search_tool_exception(self, mock_ddgs):
        """Test web search with exception."""
        # Configure mock to raise exception
        mock_ddgs.side_effect = Exception("Search API error")

        # Test the search
        result = web_search_tool.invoke({
            "query": "test search",
            "max_results": 10
        })

        # Verify error handling
        self.assertIn("error", result)
        self.assertIn("Failed to perform web search", result["error"])

    @patch('agent.langgraph_tools.web_search_tool')
    @patch('agent.langgraph_tools.crawl_website_tool')
    async def test_search_and_crawl_tool_success(self, mock_crawl_tool, mock_search_tool):
        """Test successful search and crawl."""
        # Mock search results
        mock_search_result = {
            "results": [
                {
                    "title": "Test Page",
                    "url": "https://example.com",
                    "snippet": "Test description"
                }
            ],
            "success": True
        }
        mock_search_tool.invoke.return_value = mock_search_result

        # Mock crawl results
        mock_crawl_result = {
            "markdown_content": "# Test Content\nThis is test content.",
            "saved_html_path": "/path/to/saved.html",
            "success": True
        }
        mock_crawl_tool.ainvoke.return_value = mock_crawl_result

        # Test search and crawl
        result = await search_and_crawl_tool.ainvoke({
            "query": "test search",
            "max_results": 1,
            "screenshot": False,
            "pdf": False
        })

        # Verify results
        self.assertIn("success", result)
        self.assertTrue(result["success"])
        self.assertIn("crawled_results", result)
        self.assertEqual(len(result["crawled_results"]), 1)
        self.assertEqual(result["crawled_results"][0]["title"], "Test Page")
        self.assertIn("markdown_content", result["crawled_results"][0])

    @patch('agent.langgraph_tools.web_search_tool')
    async def test_search_and_crawl_tool_search_error(self, mock_search_tool):
        """Test search and crawl with search error."""
        # Mock search error
        mock_search_tool.invoke.return_value = {"error": "Search failed"}

        # Test search and crawl
        result = await search_and_crawl_tool.ainvoke({
            "query": "test search",
            "max_results": 1
        })

        # Verify error handling
        self.assertIn("error", result)
        self.assertEqual(result["error"], "Search failed")


class TestSearchIntegration(unittest.IsolatedAsyncioTestCase):
    """Integration tests for search functionality."""

    def test_search_tool_parameters(self):
        """Test search tool parameter validation."""
        # Test with valid parameters
        try:
            result = web_search_tool.invoke({
                "query": "test",
                "max_results": 5,
                "region": "us-en",
                "safesearch": "moderate"
            })
            # Should not raise exception even if search fails
            self.assertIsInstance(result, dict)
        except Exception as e:
            # If it fails, it should be due to network/API issues, not parameter validation
            self.assertIn("search", str(e).lower())

    async def test_search_and_crawl_parameters(self):
        """Test search and crawl parameter validation."""
        # Test with valid parameters
        try:
            result = await search_and_crawl_tool.ainvoke({
                "query": "test",
                "max_results": 1,
                "screenshot": True,
                "pdf": False
            })
            # Should not raise exception even if search fails
            self.assertIsInstance(result, dict)
        except Exception as e:
            # If it fails, it should be due to network/API issues, not parameter validation
            self.assertIn("search", str(e).lower())


if __name__ == "__main__":
    # Run a simple live test if executed directly
    async def live_test():
        print("🧪 Testing Search Tools (Live Test)")
        print("-" * 40)

        try:
            # Test web search
            print("Testing web search...")
            result = web_search_tool.invoke({
                "query": "python web scraping",
                "max_results": 3
            })

            if "error" in result:
                print(f"❌ Search failed: {result['error']}")
            else:
                print(f"✅ Found {len(result['results'])} search results")
                for i, res in enumerate(result['results'][:2], 1):
                    print(f"  {i}. {res['title']}")
                    print(f"     {res['url']}")

            print("\n" + "=" * 40)
            print("🎉 Live test completed!")

        except Exception as e:
            print(f"❌ Live test failed: {e}")

    # Run live test
    asyncio.run(live_test())

    # Run unit tests
    unittest.main()
