"""
Quick test of the LangGraph agent functionality.
"""

import asyncio
from agent.langgraph_workflow import CrawlAgent


async def test_agent():
    """Test the agent with a simple request."""
    print("🚀 Testing LangGraph Agent...")
    
    try:
        agent = CrawlAgent()
        print("✅ Agent initialized successfully")
        
        # Test a simple chat request
        result = await agent.process_request("what can you do?")
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
        else:
            print("✅ Agent responded successfully:")
            if "response" in result:
                print(f"Response: {result['response'][:200]}...")
            else:
                print(f"Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agent: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_agent())
    if success:
        print("\n🎉 LangGraph agent is working! You can now run:")
        print("  python main_langgraph.py")
    else:
        print("\n⚠️  There may be configuration issues. Check:")
        print("  • <PERSON><PERSON><PERSON> is running (ollama serve)")
        print("  • Model is available (ollama pull deepseek-coder)")
        print("  • config.ini is properly configured")