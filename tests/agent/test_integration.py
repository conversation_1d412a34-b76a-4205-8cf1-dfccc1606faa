"""
Integration tests for the complete crawler workflow.

This module tests the end-to-end functionality including LangGraph workflow,
intent parsing, and complete crawling operations.
"""

import pytest
import asyncio
from agent.langgraph_workflow import create_workflow
from agent.langgraph_state import create_initial_state


class TestSingleURLWorkflow:
    """Test complete workflow with single URLs."""
    
    @pytest.mark.asyncio
    async def test_summarize_single_url(self):
        """Test summarizing a single website."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize example.com"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        assert "result" in result
        
        # Check for expected result structure
        if "result" in result and result["result"]:
            res = result["result"]
            assert isinstance(res, dict)
            
    @pytest.mark.asyncio
    async def test_impressum_single_url(self):
        """Test extracting impressum from a single website."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "impressum raumweltenheiss.de"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_extract_links_single_url(self):
        """Test extracting links from a single website."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "extract links from example.com"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
        # Check for link extraction results
        if "result" in result and result["result"]:
            res = result["result"]
            if "links" in res:
                assert isinstance(res["links"], dict)
                assert "internal" in res["links"]
                assert "external" in res["links"]


class TestMultipleURLWorkflow:
    """Test complete workflow with multiple URLs."""
    
    @pytest.mark.asyncio
    async def test_summarize_multiple_urls(self):
        """Test summarizing multiple websites."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize example.com,google.com"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
        # Check for multiple crawl results
        if "multiple_crawl_results" in result:
            results = result["multiple_crawl_results"]
            assert isinstance(results, list)
            assert len(results) == 2
            
            for res in results:
                assert "url" in res
                assert "success" in res
                
    @pytest.mark.asyncio
    async def test_impressum_multiple_urls(self):
        """Test extracting impressums from multiple websites."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "impressum example.com,raumweltenheiss.de"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
        # Check for multiple impressum results
        if "multiple_impressum_results" in result:
            results = result["multiple_impressum_results"]
            assert isinstance(results, list)
            assert len(results) == 2
            
    @pytest.mark.asyncio
    async def test_extract_links_multiple_urls(self):
        """Test extracting links from multiple websites."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "extract links from example.com,python.org"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
        # Check for multiple crawl results with links
        if "multiple_crawl_results" in result:
            results = result["multiple_crawl_results"]
            assert isinstance(results, list)
            assert len(results) == 2


class TestIntentParsing:
    """Test intent parsing for various command formats."""
    
    @pytest.mark.asyncio
    async def test_parse_comma_separated_urls(self):
        """Test parsing comma-separated URLs."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize example.com,google.com,python.org"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Should detect multiple URLs
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_parse_semicolon_separated_urls(self):
        """Test parsing semicolon-separated URLs."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "impressum example.com;raumweltenheiss.de"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Should detect multiple URLs
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_parse_pipe_separated_urls(self):
        """Test parsing pipe-separated URLs."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "extract links from example.com|python.org"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Should detect multiple URLs
        assert "error" not in result or result["error"] is None


class TestCommandOptions:
    """Test commands with various options."""
    
    @pytest.mark.asyncio
    async def test_command_with_screenshot_option(self):
        """Test command with screenshot option."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "crawl example.com --screenshot"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_command_with_pdf_option(self):
        """Test command with PDF option."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize example.com --pdf"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_command_with_multiple_options(self):
        """Test command with multiple options."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "crawl example.com --screenshot --pdf"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify workflow completion
        assert "error" not in result or result["error"] is None


class TestErrorHandling:
    """Test error handling in the workflow."""
    
    @pytest.mark.asyncio
    async def test_invalid_url_handling(self):
        """Test handling of invalid URLs."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize invalid-url-that-does-not-exist"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Should handle error gracefully
        # May have error or may complete with failed results
        assert isinstance(result, dict)
        
    @pytest.mark.asyncio
    async def test_mixed_valid_invalid_urls(self):
        """Test handling mix of valid and invalid URLs."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "summarize example.com,invalid-url-that-does-not-exist"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Should handle partial success
        assert "error" not in result or result["error"] is None
        
        # Check for mixed results
        if "multiple_crawl_results" in result:
            results = result["multiple_crawl_results"]
            assert len(results) == 2
            # Should have at least one success and one failure
            successes = [r for r in results if r.get("success")]
            failures = [r for r in results if not r.get("success")]
            assert len(successes) >= 1 or len(failures) >= 1


class TestWebSearch:
    """Test web search functionality."""
    
    @pytest.mark.asyncio
    async def test_web_search_command(self):
        """Test web search command."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "search for python web scraping"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify search completion
        assert "error" not in result or result["error"] is None
        
    @pytest.mark.asyncio
    async def test_search_and_crawl_command(self):
        """Test search and crawl command."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "search and crawl python documentation"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify search and crawl completion
        assert "error" not in result or result["error"] is None


class TestChatFunctionality:
    """Test chat functionality."""
    
    @pytest.mark.asyncio
    async def test_general_chat_command(self):
        """Test general chat command."""
        workflow = create_workflow()
        app = workflow.compile()
        
        command = "What is web scraping?"
        initial_state = create_initial_state(command)
        result = await app.ainvoke(initial_state)
        
        # Verify chat response
        assert "error" not in result or result["error"] is None
        
        # Should have chat response
        if "result" in result and result["result"]:
            res = result["result"]
            if "response" in res:
                assert isinstance(res["response"], str)
                assert len(res["response"]) > 0


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
