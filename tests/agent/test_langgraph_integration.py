"""
Simple integration test for the LangGraph agent system.

This script tests the basic functionality of the LangGraph agent
without requiring external services.
"""

from utils.logger import logger
from agent.langgraph_nodes import intent_parser_node
from agent.langgraph_state import create_initial_state, WorkflowCommands
import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


async def test_intent_parsing():
    """Test the intent parsing functionality."""
    print("🧪 Testing Intent Parsing...")

    test_cases = [
        "crawl example.com",
        "find impressum from example.com",
        "summarize https://example.com --screenshot",
        "process local file /path/to/file.html",
        "what can you do?"
    ]

    for prompt in test_cases:
        print(f"\n📝 Testing prompt: '{prompt}'")

        try:
            # Create initial state
            state = create_initial_state(prompt)
            print(f"   Initial state created: ✅")

            # Test intent parsing (this will fail without <PERSON>lla<PERSON>, but we can check structure)
            try:
                parsed_state = await intent_parser_node(state)
                print(f"   Intent parsing: ✅")
                print(
                    f"   Next action: {parsed_state.get('next_action', 'None')}")

                if parsed_state.get('error'):
                    print(
                        f"   ⚠️  Expected error (Ollama not available): {parsed_state['error']}")
                else:
                    print(
                        f"   Parsed intent: {parsed_state.get('parsed_intent', 'None')}")

            except Exception as e:
                print(f"   ⚠️  Expected error (Ollama not available): {e}")

        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
            return False

    return True


async def test_state_structure():
    """Test the state structure and workflow commands."""
    print("\n🧪 Testing State Structure...")

    try:
        # Test initial state creation
        state = create_initial_state("test prompt")

        required_keys = [
            'user_prompt', 'parsed_intent', 'url', 'local_file_path',
            'screenshot', 'pdf', 'html_content', 'markdown_content',
            'extracted_data', 'messages', 'error', 'next_action', 'result'
        ]

        for key in required_keys:
            if key not in state:
                print(f"   ❌ Missing required key: {key}")
                return False

        print("   ✅ All required state keys present")

        # Test workflow commands
        commands = [
            WorkflowCommands.CRAWL_WEBSITE,
            WorkflowCommands.CRAWL_IMPRESSUM,
            WorkflowCommands.PROCESS_LOCAL_FILE,
            WorkflowCommands.SUMMARIZE,
            WorkflowCommands.CHAT
        ]

        for command in commands:
            if not isinstance(command, str) or not command:
                print(f"   ❌ Invalid command: {command}")
                return False

        print("   ✅ Workflow commands are valid")

        return True

    except Exception as e:
        print(f"   ❌ Error testing state structure: {e}")
        return False


async def test_tools_import():
    """Test that tools can be imported successfully."""
    print("\n🧪 Testing Tools Import...")

    try:
        from agent.langgraph_tools import CRAWL_TOOLS
        print(f"   ✅ Successfully imported {len(CRAWL_TOOLS)} tools")

        # Check that tools have the expected structure
        for tool in CRAWL_TOOLS:
            if not hasattr(tool, 'name') or not hasattr(tool, 'description'):
                print(f"   ❌ Tool missing required attributes: {tool}")
                return False

        print("   ✅ All tools have required attributes")
        return True

    except Exception as e:
        print(f"   ❌ Error importing tools: {e}")
        return False


async def main():
    """Run all integration tests."""
    print("🚀 LangGraph Agent Integration Tests")
    print("=" * 50)

    tests = [
        ("State Structure", test_state_structure),
        ("Tools Import", test_tools_import),
        ("Intent Parsing", test_intent_parsing),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        try:
            if await test_func():
                print(f"✅ {test_name} Test: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} Test: FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test: ERROR - {e}")

    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The LangGraph agent is ready to use.")
        print("\nTo run the agent:")
        print("  python main_langgraph.py")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\nCommon issues:")
        print("  • Make sure Ollama is installed and running")
        print("  • Check that all dependencies are installed")
        print("  • Verify the config.ini file is properly set up")

    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
