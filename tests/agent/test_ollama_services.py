"""
Unit tests for the refactored Ollama services.
Tests each service class individually with mocked HTTP responses.
"""

import unittest
from unittest.mock import AsyncMock, patch, MagicMock
import pytest
import httpx

from agent.ollama.config import OllamaConfig
from agent.ollama.services.summary_service import SummaryService
from agent.ollama.services.chat_service import ChatService
from agent.ollama.services.command_parsing_service import CommandParsingService
from agent.ollama.services.company_data_service import CompanyDataExtractionService
from agent.ollama.factory import OllamaServiceFactory
from agent.ollama.exceptions import (
    OllamaConnectionError,
    OllamaTimeoutError,
    OllamaModelNotFoundError,
    OllamaResponseError,
)


class TestOllamaConfig(unittest.TestCase):
    """Test the configuration management."""
    
    def test_default_config_creation(self):
        """Test creating default configuration."""
        config = OllamaConfig.create_default()
        self.assertIsNotNone(config.base_url)
        self.assertGreater(config.retry.max_attempts, 0)
        self.assertIsNotNone(config.models.primary_model)
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = OllamaConfig.create_default()
        # Should not raise
        config.validate()
        
        # Test invalid config
        config.base_url = ""
        with self.assertRaises(ValueError):
            config.validate()


class TestSummaryService(unittest.IsolatedAsyncioTestCase):
    """Test the summary service."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = OllamaConfig.create_default()
        self.service = SummaryService(self.config)
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    async def test_generate_summary_success(self, mock_client_class):
        """Test successful summary generation."""
        # Mock the HTTP response
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": "Test summary"}
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        # Test the service
        result = await self.service.generate_summary("Test content")
        
        self.assertEqual(result, "Test summary")
        mock_client.post.assert_called_once()
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    async def test_generate_summary_empty_content(self, mock_client_class):
        """Test summary generation with empty content."""
        result = await self.service.generate_summary("")
        self.assertEqual(result, "No content provided for summarization.")
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    async def test_generate_summary_http_error(self, mock_client_class):
        """Test summary generation with HTTP error."""
        mock_client = AsyncMock()
        mock_client.post.side_effect = httpx.RequestError("Connection failed")
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        with self.assertRaises(Exception):
            await self.service.generate_summary("Test content")


class TestChatService(unittest.IsolatedAsyncioTestCase):
    """Test the chat service."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = OllamaConfig.create_default()
        self.service = ChatService(self.config)
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    async def test_get_chat_response_success(self, mock_client_class):
        """Test successful chat response."""
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": "Test chat response"}
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        result = await self.service.get_chat_response("Hello")
        
        self.assertEqual(result, "Test chat response")
        mock_client.post.assert_called_once()
    
    async def test_get_chat_response_empty_prompt(self):
        """Test chat response with empty prompt."""
        result = await self.service.get_chat_response("")
        self.assertEqual(result, "Please provide a question or prompt for me to respond to.")


class TestCommandParsingService(unittest.IsolatedAsyncioTestCase):
    """Test the command parsing service."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = OllamaConfig.create_default()
        self.service = CommandParsingService(self.config)
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    @patch('utils.json_utils.extract_json_from_response')
    async def test_parse_command_success(self, mock_extract_json, mock_client_class):
        """Test successful command parsing."""
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": '{"command": "summarize", "url": "example.com"}'}
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        mock_extract_json.return_value = {"command": "summarize", "url": "example.com"}
        
        result = await self.service.parse_command("summarize example.com")
        
        self.assertEqual(result["command"], "summarize")
        self.assertEqual(result["url"], "example.com")
    
    async def test_parse_command_empty_prompt(self):
        """Test command parsing with empty prompt."""
        result = await self.service.parse_command("")
        self.assertEqual(result, {"command": "chat", "url": None})
    
    def test_validate_parsed_command(self):
        """Test command validation."""
        # Valid command
        valid_command = {"command": "summarize", "url": "example.com"}
        self.assertTrue(self.service.validate_parsed_command(valid_command))
        
        # Invalid command - missing command key
        invalid_command = {"url": "example.com"}
        self.assertFalse(self.service.validate_parsed_command(invalid_command))
        
        # Invalid command - not a dict
        self.assertFalse(self.service.validate_parsed_command("not a dict"))


class TestCompanyDataExtractionService(unittest.IsolatedAsyncioTestCase):
    """Test the company data extraction service."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = OllamaConfig.create_default()
        self.service = CompanyDataExtractionService(self.config)
    
    @patch('agent.ollama.base_client.httpx.AsyncClient')
    @patch('utils.json_utils.extract_json_from_response')
    @patch('utils.preprocessing_utils.preprocess_impressum_content')
    async def test_extract_company_data_success(self, mock_preprocess, mock_extract_json, mock_client_class):
        """Test successful company data extraction."""
        mock_preprocess.return_value = "Cleaned content"
        
        mock_response = MagicMock()
        mock_response.json.return_value = {"response": '{"company_name": "Test Corp"}'}
        mock_response.raise_for_status.return_value = None
        
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        mock_extract_json.return_value = {"company_name": "Test Corp"}
        
        result = await self.service.extract_company_data("Test content", "https://example.com")
        
        self.assertIn("company_name", result)
        self.assertEqual(result["company_name"], "Test Corp")
    
    async def test_extract_company_data_empty_content(self):
        """Test extraction with empty content."""
        result = await self.service.extract_company_data("", "https://example.com")
        self.assertIn("error", result)
        self.assertEqual(result["error"], "No content provided for extraction")
    
    async def test_extract_company_data_empty_url(self):
        """Test extraction with empty URL."""
        result = await self.service.extract_company_data("Test content", "")
        self.assertIn("error", result)
        self.assertEqual(result["error"], "No URL provided for context")
    
    def test_extract_tld(self):
        """Test TLD extraction."""
        tld = self.service._extract_tld("https://example.com")
        self.assertEqual(tld, "com")
        
        tld = self.service._extract_tld("https://example.de")
        self.assertEqual(tld, "de")
        
        # Test fallback
        tld = self.service._extract_tld("invalid-url")
        self.assertEqual(tld, "com")
    
    def test_should_retry_extraction(self):
        """Test retry logic."""
        # Should retry for errors
        self.assertTrue(self.service._should_retry_extraction({"error": "test"}))
        
        # Should retry for poor results
        poor_result = {field: "Not available" for field in self.service.REQUIRED_FIELDS}
        self.assertTrue(self.service._should_retry_extraction(poor_result))
        
        # Should not retry for good results
        good_result = {"company_name": "Test Corp", "city": "Test City", "country": "Test Country"}
        self.assertFalse(self.service._should_retry_extraction(good_result))
    
    def test_score_extraction_result(self):
        """Test result scoring."""
        # Empty result
        self.assertEqual(self.service._score_extraction_result({}), 0)
        
        # Error result
        self.assertEqual(self.service._score_extraction_result({"error": "test"}), 0)
        
        # Partial result
        partial_result = {"company_name": "Test Corp", "city": "Test City"}
        score = self.service._score_extraction_result(partial_result)
        self.assertEqual(score, 2)


class TestOllamaServiceFactory(unittest.TestCase):
    """Test the service factory."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.factory = OllamaServiceFactory.create_default()
    
    def test_get_summary_service(self):
        """Test getting summary service."""
        service1 = self.factory.get_summary_service()
        service2 = self.factory.get_summary_service()
        
        # Should return the same instance (cached)
        self.assertIs(service1, service2)
        self.assertIsInstance(service1, SummaryService)
    
    def test_get_chat_service(self):
        """Test getting chat service."""
        service = self.factory.get_chat_service()
        self.assertIsInstance(service, ChatService)
    
    def test_get_command_parsing_service(self):
        """Test getting command parsing service."""
        service = self.factory.get_command_parsing_service()
        self.assertIsInstance(service, CommandParsingService)
    
    def test_get_company_data_service(self):
        """Test getting company data service."""
        service = self.factory.get_company_data_service()
        self.assertIsInstance(service, CompanyDataExtractionService)
    
    def test_clear_cache(self):
        """Test cache clearing."""
        service1 = self.factory.get_summary_service()
        self.factory.clear_cache()
        service2 = self.factory.get_summary_service()
        
        # Should be different instances after cache clear
        self.assertIsNot(service1, service2)
    
    def test_update_config(self):
        """Test configuration update."""
        new_config = OllamaConfig.create_default()
        new_config.debug_logging = False
        
        self.factory.update_config(new_config)
        self.assertFalse(self.factory.config.debug_logging)


if __name__ == '__main__':
    unittest.main()
