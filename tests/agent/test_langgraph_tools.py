"""
Tests for LangGraph tools.

This module tests the LangGraph tool functions that wrap the crawler
functionality for use in the agentic workflow.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock

# Import the tools to test
from agent.langgraph_tools import (
    crawl_website_tool,
    crawl_with_links_tool,
    crawl_multiple_websites_tool,
    crawl_multiple_impressums_tool,
    crawl_impressum_tool,
    crawl_local_file_tool,
    web_search_tool,
    search_and_crawl_tool,
    extract_company_data_tool,
    summarize_content_tool
)


class TestCrawlWebsiteTool:
    """Test the crawl_website_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_website_tool_success(self):
        """Test successful website crawling via tool."""
        result = await crawl_website_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": False,
            "pdf": False
        })
        
        # Verify result structure
        assert isinstance(result, dict)
        assert "markdown_content" in result
        assert "html_path" in result
        assert "page_title" in result
        assert result["markdown_content"] is not None
        
    @pytest.mark.asyncio
    async def test_crawl_website_tool_with_options(self):
        """Test website crawling tool with screenshot and PDF."""
        result = await crawl_website_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": True,
            "pdf": True
        })
        
        # Verify additional files were created
        assert "screenshot_path" in result
        assert "pdf_path" in result
        assert result["screenshot_path"] is not None
        assert result["pdf_path"] is not None
        
    @pytest.mark.asyncio
    async def test_crawl_website_tool_invalid_url(self):
        """Test website crawling tool with invalid URL."""
        result = await crawl_website_tool.ainvoke({
            "url": "invalid-url",
            "screenshot": False,
            "pdf": False
        })
        
        # Should return error
        assert "error" in result


class TestCrawlWithLinksTool:
    """Test the crawl_with_links_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_with_links_tool_success(self):
        """Test successful link extraction via tool."""
        result = await crawl_with_links_tool.ainvoke({
            "url": "https://www.example.com",
            "screenshot": False,
            "pdf": False,
            "extract_links": True,
            "save_links": True
        })
        
        # Verify link extraction results
        assert isinstance(result, dict)
        assert "links_data" in result
        assert "links_file_path" in result
        assert result["links_data"] is not None
        
        # Verify link structure
        links = result["links_data"]
        assert "internal" in links
        assert "external" in links
        assert isinstance(links["internal"], list)
        assert isinstance(links["external"], list)


class TestCrawlMultipleWebsitesTool:
    """Test the crawl_multiple_websites_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_tool_list(self):
        """Test multiple website crawling with list of URLs."""
        urls = ["https://www.example.com", "https://www.google.com"]
        
        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": urls,
            "screenshot": False,
            "pdf": False,
            "extract_links": False
        })
        
        # Verify batch results
        assert isinstance(result, dict)
        assert "total_urls" in result
        assert "successful_crawls" in result
        assert "failed_crawls" in result
        assert "results" in result
        assert result["total_urls"] == 2
        assert result["successful_crawls"] >= 0
        assert len(result["results"]) == 2
        
    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_tool_single_string(self):
        """Test multiple website crawling with single URL string."""
        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": "https://www.example.com",
            "screenshot": False,
            "pdf": False,
            "extract_links": False
        })
        
        # Should handle single URL as string
        assert result["total_urls"] == 1
        assert len(result["results"]) == 1
        
    @pytest.mark.asyncio
    async def test_crawl_multiple_websites_tool_with_links(self):
        """Test multiple website crawling with link extraction."""
        result = await crawl_multiple_websites_tool.ainvoke({
            "urls": ["https://www.example.com"],
            "screenshot": False,
            "pdf": False,
            "extract_links": True
        })
        
        # Verify link extraction in batch results
        assert result["total_urls"] == 1
        if result["successful_crawls"] > 0:
            first_result = result["results"][0]
            if first_result["success"]:
                assert first_result["links_data"] is not None


class TestCrawlMultipleImpressumsTool:
    """Test the crawl_multiple_impressums_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_multiple_impressums_tool_list(self):
        """Test multiple impressum crawling with list of URLs."""
        urls = ["https://www.example.com", "https://www.raumweltenheiss.de"]
        
        result = await crawl_multiple_impressums_tool.ainvoke({
            "urls": urls
        })
        
        # Verify batch impressum results
        assert isinstance(result, dict)
        assert "total_urls" in result
        assert "successful_crawls" in result
        assert "failed_crawls" in result
        assert "results" in result
        assert result["total_urls"] == 2
        assert len(result["results"]) == 2
        
    @pytest.mark.asyncio
    async def test_crawl_multiple_impressums_tool_single_string(self):
        """Test multiple impressum crawling with single URL string."""
        result = await crawl_multiple_impressums_tool.ainvoke({
            "urls": "https://www.raumweltenheiss.de"
        })
        
        # Should handle single URL as string
        assert result["total_urls"] == 1
        assert len(result["results"]) == 1


class TestCrawlImpressumTool:
    """Test the crawl_impressum_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_impressum_tool_success(self):
        """Test successful impressum crawling via tool."""
        result = await crawl_impressum_tool.ainvoke({
            "base_url": "https://www.raumweltenheiss.de"
        })
        
        # Verify impressum result structure
        assert isinstance(result, dict)
        # May succeed or fail depending on site structure
        if "error" not in result:
            assert "markdown_content" in result
            assert "html_path" in result


class TestCrawlLocalFileTool:
    """Test the crawl_local_file_tool."""
    
    @pytest.mark.asyncio
    async def test_crawl_local_file_tool_not_found(self):
        """Test local file crawling with non-existent file."""
        result = await crawl_local_file_tool.ainvoke({
            "local_file_path": "/path/that/does/not/exist.html"
        })
        
        # Should return error for non-existent file
        assert "error" in result


class TestWebSearchTool:
    """Test the web_search_tool."""
    
    @pytest.mark.asyncio
    async def test_web_search_tool_success(self):
        """Test successful web search via tool."""
        result = await web_search_tool.ainvoke({
            "query": "python web scraping",
            "num_results": 3
        })
        
        # Verify search results
        assert isinstance(result, dict)
        assert "results" in result
        assert isinstance(result["results"], list)
        assert len(result["results"]) <= 3
        
        # Verify result structure
        if result["results"]:
            first_result = result["results"][0]
            assert "title" in first_result
            assert "url" in first_result
            assert "snippet" in first_result


class TestSearchAndCrawlTool:
    """Test the search_and_crawl_tool."""
    
    @pytest.mark.asyncio
    async def test_search_and_crawl_tool_success(self):
        """Test successful search and crawl via tool."""
        result = await search_and_crawl_tool.ainvoke({
            "query": "python documentation",
            "num_results": 2,
            "crawl_top_n": 1
        })
        
        # Verify search and crawl results
        assert isinstance(result, dict)
        assert "search_results" in result
        assert "crawl_results" in result
        
        # Verify search results
        assert isinstance(result["search_results"], list)
        
        # Verify crawl results
        if result["crawl_results"]:
            assert isinstance(result["crawl_results"], list)
            first_crawl = result["crawl_results"][0]
            assert "url" in first_crawl
            assert "success" in first_crawl


class TestExtractCompanyDataTool:
    """Test the extract_company_data_tool."""
    
    @pytest.mark.asyncio
    async def test_extract_company_data_tool_success(self):
        """Test company data extraction via tool."""
        # Mock impressum content
        mock_content = """
        Impressum
        
        Firma: Test GmbH
        Adresse: Teststraße 123, 12345 Teststadt
        Telefon: +49 **********
        E-Mail: <EMAIL>
        """
        
        result = await extract_company_data_tool.ainvoke({
            "content": mock_content,
            "base_url": "https://www.test.de"
        })
        
        # Verify extraction result
        assert isinstance(result, dict)
        # May succeed or fail depending on Ollama availability
        if "error" not in result:
            assert "company_data" in result
            assert "json_file_path" in result


class TestSummarizeContentTool:
    """Test the summarize_content_tool."""
    
    @pytest.mark.asyncio
    async def test_summarize_content_tool_success(self):
        """Test content summarization via tool."""
        mock_content = """
        This is a test article about web scraping.
        Web scraping is the process of extracting data from websites.
        It involves making HTTP requests and parsing HTML content.
        There are many tools available for web scraping including Python libraries.
        """
        
        result = await summarize_content_tool.ainvoke({
            "content": mock_content,
            "base_url": "https://www.example.com"
        })
        
        # Verify summarization result
        assert isinstance(result, dict)
        # May succeed or fail depending on Ollama availability
        if "error" not in result:
            assert "summary" in result
            assert "summary_file_path" in result


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
