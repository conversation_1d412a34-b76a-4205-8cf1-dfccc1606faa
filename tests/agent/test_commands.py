import unittest
from unittest.mock import patch, AsyncMock, MagicMock

from agent.commands import summarize_website


class TestCommands(unittest.IsolatedAsyncioTestCase):

    @patch("agent.commands.save_summary_to_dir")
    @patch("agent.commands.get_summary_from_ollama")
    @patch("agent.commands.crawl_website")
    async def test_summarize_website_with_media(self, mock_crawl, mock_get_summary, mock_save_summary):
        """Should correctly handle screenshot and PDF requests."""
        # Arrange
        mock_crawl.return_value = (
            "# Markdown content",
            "/path/to/html.html",
            "/path/to/screenshot.png",
            "/path/to/document.pdf"
        )
        mock_get_summary.return_value = "This is a summary"
        mock_save_summary.return_value = "/path/to/summary.md"

        # Act
        result = await summarize_website("https://example.com", screenshot=True, pdf=True)

        # Assert
        self.assertEqual(result["summary"], "This is a summary")
        self.assertEqual(result["files"]["html"], "/path/to/html.html")
        self.assertEqual(result["files"]["summary"], "/path/to/summary.md")
        self.assertEqual(result["files"]["screenshot"],
                         "/path/to/screenshot.png")
        self.assertEqual(result["files"]["pdf"], "/path/to/document.pdf")

        # Verify crawl_website was called with the correct parameters
        mock_crawl.assert_called_once_with(
            "https://example.com",
            screenshot=True,
            pdf=True
        )

        # Verify the summary was saved
        mock_save_summary.assert_called_once_with(
            "This is a summary",
            "https://example.com"
        )
