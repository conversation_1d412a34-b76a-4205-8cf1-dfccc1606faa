import unittest
from utils.preprocessing_utils import (
    preprocess_impressum_content,
    preprocess_html_content,
    extract_text_from_html
)


class TestPreprocessingUtils(unittest.TestCase):

    def test_preprocess_impressum_content(self):
        """Test that preprocess_impressum_content correctly cleans HTML content."""
        # Test input with various HTML elements
        test_html = """
        <html>
        <head>
            <script>var x = 10;</script>
            <style>.test { color: red; }</style>
        </head>
        <body>
            <!-- This is a comment -->
            <div>Company Name: Test GmbH</div>
            <p>Address: Test Street 123</p>
            <span>Email: <EMAIL></span>
            <script>alert('test');</script>
            <div class="long-string">aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</div>
        </body>
        </html>
        """

        # Expected output after preprocessing
        expected_output = "Company Name: Test GmbH Address: Test Street 123 Email: <EMAIL>"

        # Process the HTML
        result = preprocess_impressum_content(test_html)

        # Check that scripts, styles, comments, and HTML tags are removed
        self.assertNotIn("<script>", result)
        self.assertNotIn("<style>", result)
        self.assertNotIn("<!--", result)
        self.assertNotIn("<div>", result)

        # Check that content is preserved
        self.assertIn("Company Name: Test GmbH", result)
        self.assertIn("Address: Test Street 123", result)
        self.assertIn("Email: <EMAIL>", result)

        # Check that long strings are removed
        self.assertNotIn("a" * 100, result)

        # Check that multiple spaces are normalized
        self.assertNotIn("  ", result)

    def test_preprocess_html_content(self):
        """Test that preprocess_html_content correctly cleans HTML content with max_length parameter."""
        # Test input with various HTML elements
        test_html = """
        <html>
        <head>
            <meta charset="UTF-8">
            <link rel="stylesheet" href="style.css">
            <script>var x = 10;</script>
            <style>.test { color: red; }</style>
        </head>
        <body>
            <!-- This is a comment -->
            <div>Company Name: Test GmbH</div>
            <p>Address: Test Street 123</p>
            <span>Email: <EMAIL></span>
        </body>
        </html>
        """

        # Process the HTML with a small max_length
        result = preprocess_html_content(test_html, max_length=50)

        # Check that the result is truncated to max_length
        self.assertLessEqual(len(result), 50)

        # Process with default max_length
        result_default = preprocess_html_content(test_html)

        # Check that meta and link tags are removed
        self.assertNotIn("<meta", result_default)
        self.assertNotIn("<link", result_default)

    def test_extract_text_from_html(self):
        """Test that extract_text_from_html correctly extracts readable text with proper formatting."""
        # Test input with various HTML elements and structure
        test_html = """
        <html>
        <head>
            <title>Test Page</title>
            <script>var x = 10;</script>
        </head>
        <body>
            <h1>Company Information</h1>
            <div>
                <p>Company Name: Test GmbH</p>
                <p>Address: <br>Test Street 123<br>12345 Test City</p>
            </div>
            <ul>
                <li>Phone: +49 **********</li>
                <li>Email: <EMAIL></li>
            </ul>
        </body>
        </html>
        """

        # Process the HTML
        result = extract_text_from_html(test_html)

        # Check that block elements are replaced with newlines
        self.assertIn("Company Information\n", result)
        self.assertIn("Company Name: Test GmbH\n", result)

        # Check that content is preserved with proper formatting
        self.assertIn("Test Street 123", result)
        self.assertIn("12345 Test City", result)
        self.assertIn("Phone: +49 **********", result)
        self.assertIn("Email: <EMAIL>", result)

        # Check that script and head content is removed
        self.assertNotIn("var x = 10", result)
        self.assertNotIn("Test Page", result)  # Title should be removed

        # Check that HTML tags are removed
        self.assertNotIn("<", result)
        self.assertNotIn(">", result)


if __name__ == "__main__":
    unittest.main()
