import unittest
from utils.find_link_utils import find_impressum_link


class TestFindImpressumLink(unittest.IsolatedAsyncioTestCase):
    BASE_URL = "https://example.com"

    async def test_find_by_text_impressum(self):
        """Should find the link by the text 'Impressum'."""
        html = '<footer><a href="/imprint-page">Our Impressum</a></footer>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/imprint-page")

    async def test_find_by_href_impressum(self):
        """Should find the link by the href containing 'impressum'."""
        html = '<div><a href="/impressum.html">Legal Stuff</a></div>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/impressum.html")

    async def test_find_by_text_case_insensitive(self):
        """Should find the link regardless of case."""
        html = '<a href="/imprint">IMPRESSUM</a>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/imprint")

    async def test_find_legal_notice_with_space(self):
        """Should find 'Legal Notice' with a space in the text."""
        html = '<a href="/legal">Legal Notice</a>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/legal")

    async def test_find_legal_notice_with_hyphen_in_href(self):
        """Should find 'legal-notice' with a hyphen in the href."""
        html = '<a href="/company/legal-notice">Company Info</a>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/company/legal-notice")

    async def test_find_imprint(self):
        """Should find the link by the text 'Imprint'."""
        html = '<footer><a href="/imprint-page">Imprint</a></footer>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/imprint-page")

    async def test_handles_absolute_url_in_href(self):
        """Should return the absolute URL directly if found."""
        html = '<a href="https://www.another.com/imprint">Impressum</a>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://www.another.com/imprint")

    async def test_no_match_found(self):
        """Should return None if no relevant links are found."""
        html = '<footer><a href="/contact">Contact Us</a><a href="/about">About</a></footer>'
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertIsNone(result)

    async def test_empty_html(self):
        """Should return None for empty HTML content."""
        html = ""
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertIsNone(result)

    async def test_html_with_no_links(self):
        """Should return None if the HTML contains no anchor tags."""
        html = "<p>This is a paragraph without any links.</p>"
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertIsNone(result)

    async def test_first_match_is_returned(self):
        """Should return the first link that matches the criteria."""
        html = """
        <a href="/impressum">First Match</a>
        <a href="/legal-notice">Second Match</a>
        """
        result = await find_impressum_link(html, self.BASE_URL)
        self.assertEqual(result, "https://example.com/impressum")
