import os
import tempfile
import unittest
from unittest.mock import patch, mock_open, MagicMock
from utils.file_utils import save_json_to_dir, save_html_to_dir, read_local_file, save_summary_to_dir, save_media_to_dir


class TestFileUtils(unittest.TestCase):

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.json.dump")
    @patch("utils.file_utils.datetime")
    def test_save_json_to_dir(self, mock_datetime, mock_json_dump, mock_open_func, mock_makedirs):
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_data = {"key": "value"}
        test_url = "https://example.com/page"

        # Act
        file_path = save_json_to_dir(test_data, test_url, "test_results")

        # Assert
        mock_makedirs.assert_called_once_with(
            "test_results/example.com", exist_ok=True)
        mock_open_func.assert_called_once_with(
            "test_results/example.com/20230101_120000.json", "w", encoding="utf-8")
        mock_json_dump.assert_called_once_with(
            test_data, mock_open_func(), indent=4, ensure_ascii=False)
        self.assertEqual(
            file_path, "test_results/example.com/20230101_120000.json")

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_summary_to_dir(self, mock_datetime, mock_open_func, mock_makedirs):
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_summary = "This is a test summary."
        test_url = "https://example.com/some/page"

        # Act
        file_path = save_summary_to_dir(test_summary, test_url, "test_results")

        # Assert
        mock_makedirs.assert_called_once_with(
            "test_results/example.com", exist_ok=True)
        expected_path = "test_results/example.com/summary_20230101_120000.md"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")
        mock_open_func().write.assert_called_once_with(test_summary)
        self.assertEqual(file_path, expected_path)

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_html_to_dir(self, mock_datetime, mock_open_func, mock_makedirs):
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_html = "<html><body>Test</body></html>"
        test_url = "https://example.com/about-us.html"

        # Act
        file_path = save_html_to_dir(test_html, test_url, "test_html")

        # Assert
        mock_makedirs.assert_called_once_with(
            "test_html/example.com", exist_ok=True)
        expected_path = "test_html/example.com/about-us_20230101_120000.html"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")
        mock_open_func().write.assert_called_once_with(test_html)
        self.assertEqual(file_path, expected_path)

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_html_to_dir_root_url(self, mock_datetime, mock_open_func, mock_makedirs):
        """Test that a root URL correctly generates 'index' as the page name."""
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance
        save_html_to_dir("<html></html>", "http://example.com/", "test_html")
        expected_path = "test_html/example.com/index_20230101_120000.html"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_html_to_dir_with_special_chars_in_path(self, mock_datetime, mock_open_func, mock_makedirs):
        """Should correctly sanitize special characters in the URL path for the filename."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_html = "<html></html>"
        test_url = "https://example.com/path with spaces/and$pecial@chars"

        # Act
        save_html_to_dir(test_html, test_url, "test_html")

        # Assert
        mock_makedirs.assert_called_once_with(
            "test_html/example.com", exist_ok=True)
        expected_path = "test_html/example.com/and_pecial_chars_20230101_120000.html"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_html_to_dir_with_long_path(self, mock_datetime, mock_open_func, mock_makedirs):
        """Should truncate a long page name to 50 characters."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_html = "<html></html>"
        long_path_segment = "a" * 100
        test_url = f"https://example.com/{long_path_segment}"

        # Act
        save_html_to_dir(test_html, test_url, "test_html")

        # Assert
        truncated_name = "a" * 50
        expected_path = f"test_html/example.com/{truncated_name}_20230101_120000.html"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.json.dump")
    @patch("utils.file_utils.datetime")
    def test_save_json_to_dir_with_www(self, mock_datetime, mock_json_dump, mock_open_func, mock_makedirs):
        """Should remove 'www.' from the domain when creating the directory."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_data = {"key": "value"}
        test_url = "https://www.example.com/page"

        # Act
        save_json_to_dir(test_data, test_url, "test_results")

        # Assert
        # The directory should be 'example.com', not 'www.example.com'
        mock_makedirs.assert_called_once_with(
            "test_results/example.com", exist_ok=True)
        expected_path = "test_results/example.com/20230101_120000.json"
        mock_open_func.assert_called_once_with(
            expected_path, "w", encoding="utf-8")

    def test_read_local_file_success(self):
        """Should read and return the content of an existing file."""
        # Arrange
        content = "<html>Hello World</html>"
        # Use a temporary file that is automatically deleted on close
        with tempfile.NamedTemporaryFile(mode='w+', delete=True, suffix=".html", encoding='utf-8') as tmp:
            tmp.write(content)
            tmp.seek(0)  # Go back to the beginning of the file to read it
            tmp_path = tmp.name

            # Act
            result = read_local_file(tmp_path)

            # Assert
            self.assertEqual(result, content)

    @patch('utils.file_utils.os.path.exists', return_value=False)
    @patch('utils.file_utils.print')
    def test_read_local_file_not_found(self, mock_print, mock_exists):
        """Should return None and print an error if the file does not exist."""
        # Arrange
        fake_path = "/fake/path/to/file.html"

        # Act
        result = read_local_file(fake_path)

        # Assert
        self.assertIsNone(result)
        mock_exists.assert_called_once()
        mock_print.assert_called_once()
        self.assertIn("File does not exist", mock_print.call_args[0][0])

    @patch('utils.file_utils.os.path.exists', return_value=True)
    @patch('utils.file_utils.open', side_effect=IOError("Permission denied"))
    @patch('utils.file_utils.print')
    def test_read_local_file_io_error(self, mock_print, mock_open, mock_exists):
        """Should return None and print an error if an IOError occurs."""
        # Arrange
        fake_path = "/fake/path/to/file.html"

        # Act
        result = read_local_file(fake_path)

        # Assert
        self.assertIsNone(result)
        mock_exists.assert_called_once()
        mock_open.assert_called_once_with(
            os.path.abspath(fake_path), "r", encoding="utf-8")
        mock_print.assert_called_once()
        self.assertIn("Could not read file", mock_print.call_args[0][0])

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_media_to_dir_screenshot(self, mock_datetime, mock_open_func, mock_makedirs):
        """Should correctly save screenshot binary data to a file."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_binary = b"fake screenshot data"
        test_url = "https://example.com/about-us"

        # Act
        file_path = save_media_to_dir(test_binary, test_url, "screenshots", "png")

        # Assert
        mock_makedirs.assert_called_once_with(
            "screenshots/example.com", exist_ok=True)
        expected_path = "screenshots/example.com/about-us_20230101_120000.png"
        mock_open_func.assert_called_once_with(
            expected_path, "wb")  # Note: binary mode
        mock_open_func().write.assert_called_once_with(test_binary)
        self.assertEqual(file_path, expected_path)

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_media_to_dir_pdf(self, mock_datetime, mock_open_func, mock_makedirs):
        """Should correctly save PDF binary data to a file."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_binary = b"fake PDF data"
        test_url = "https://example.com/about-us"

        # Act
        file_path = save_media_to_dir(test_binary, test_url, "pdfs", "pdf")

        # Assert
        mock_makedirs.assert_called_once_with(
            "pdfs/example.com", exist_ok=True)
        expected_path = "pdfs/example.com/about-us_20230101_120000.pdf"
        mock_open_func.assert_called_once_with(
            expected_path, "wb")  # Note: binary mode
        mock_open_func().write.assert_called_once_with(test_binary)
        self.assertEqual(file_path, expected_path)

    @patch("utils.file_utils.os.makedirs")
    @patch("utils.file_utils.open", new_callable=mock_open)
    @patch("utils.file_utils.datetime")
    def test_save_media_to_dir_with_long_path(self, mock_datetime, mock_open_func, mock_makedirs):
        """Should truncate a long page name to 50 characters for media files."""
        # Arrange
        mock_dt_instance = MagicMock()
        mock_dt_instance.strftime.return_value = "20230101_120000"
        mock_datetime.now.return_value = mock_dt_instance

        test_binary = b"test data"
        long_path_segment = "a" * 100
        test_url = f"https://example.com/{long_path_segment}"

        # Act
        save_media_to_dir(test_binary, test_url, "screenshots", "png")

        # Assert
        truncated_name = "a" * 50
        expected_path = f"screenshots/example.com/{truncated_name}_20230101_120000.png"
        mock_open_func.assert_called_once_with(
            expected_path, "wb")
