import unittest
import logging
from unittest.mock import patch
from utils.logger import setup_logger


class TestLogger(unittest.TestCase):

    @patch("utils.logger.logging.StreamHandler")
    @patch("utils.logger.logging.FileHandler")
    def test_setup_logger(self, mock_file_handler, mock_stream_handler):
        """Should set up a logger with the correct level and handlers."""
        # Ensure we start with a clean slate for this test
        logger_name = "TestLogger"
        logging.getLogger(logger_name).handlers = []

        logger = setup_logger(name=logger_name, level=logging.DEBUG)

        self.assertEqual(logger.name, logger_name)
        self.assertEqual(logger.level, logging.DEBUG)
        self.assertEqual(len(logger.handlers), 2)
        mock_stream_handler.assert_called_once()
        mock_file_handler.assert_called_once()

    def test_logger_is_singleton(self):
        """Should return the same logger instance on subsequent calls."""
        logger1 = setup_logger(name="SingletonTest")
        logger2 = setup_logger(name="SingletonTest")
        self.assertIs(logger1, logger2)
