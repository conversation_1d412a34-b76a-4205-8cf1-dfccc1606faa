import unittest
from utils.json_utils import extract_json_from_response


class TestJsonUtils(unittest.TestCase):

    def test_extract_clean_json(self):
        """Should extract a simple, clean JSON string."""
        text = '{"key": "value", "number": 123}'
        expected = {"key": "value", "number": 123}
        self.assertEqual(extract_json_from_response(text), expected)

    def test_extract_json_with_surrounding_text(self):
        """Should extract JSON when it's surrounded by other text."""
        text = 'Here is the data you requested: {"a": 1, "b": [2, 3]}. Please review.'
        expected = {"a": 1, "b": [2, 3]}
        self.assertEqual(extract_json_from_response(text), expected)

    def test_extract_json_from_markdown_block(self):
        """Should extract JSON from a markdown code block."""
        text = '```json\n{"user": "test", "active": true}\n```'
        expected = {"user": "test", "active": True}
        self.assertEqual(extract_json_from_response(text), expected)

    def test_malformed_json(self):
        """Should return an empty dict for malformed JSON."""
        text = '{"key": "value", "malformed": }'
        self.assertEqual(extract_json_from_response(text), {})

    def test_no_json_in_string(self):
        """Should return an empty dict if no JSON is present."""
        text = "This is just a regular sentence without any JSON."
        self.assertEqual(extract_json_from_response(text), {})

    def test_empty_string(self):
        """Should return an empty dict for an empty string."""
        text = ""
        self.assertEqual(extract_json_from_response(text), {})
