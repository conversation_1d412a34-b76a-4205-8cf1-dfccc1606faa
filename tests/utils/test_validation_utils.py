import unittest
from utils.validation_utils import is_valid_url


class TestValidationUtils(unittest.TestCase):

    def test_valid_http_url(self):
        """Should return True for a valid http URL."""
        self.assertTrue(is_valid_url("http://example.com"))

    def test_valid_https_url(self):
        """Should return True for a valid https URL."""
        self.assertTrue(is_valid_url("https://www.example.com/path?query=1"))

    def test_url_without_scheme(self):
        """Should return False for a URL without a scheme."""
        self.assertFalse(is_valid_url("example.com"))

    def test_url_without_netloc(self):
        """Should return False for a URL without a network location."""
        self.assertFalse(is_valid_url("http:///path"))

    def test_invalid_scheme(self):
        """Should return False for an invalid scheme."""
        self.assertFalse(is_valid_url("htp://example.com"))

    def test_empty_string(self):
        """Should return False for an empty string."""
        self.assertFalse(is_valid_url(""))

    def test_none_input(self):
        """Should return False for None input."""
        self.assertFalse(is_valid_url(None))
