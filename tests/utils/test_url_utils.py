import unittest
from utils.url_utils import normalize_url


class TestUrlUtils(unittest.TestCase):

    def test_normalize_url_with_scheme(self):
        """Should return the URL as is if it already has a scheme."""
        url = "http://example.com"
        self.assertEqual(normalize_url(url), "http://example.com")

    def test_normalize_url_without_scheme(self):
        """Should add 'https://' to a URL without a scheme."""
        url = "example.com"
        self.assertEqual(normalize_url(url), "https://example.com")

    def test_normalize_url_with_www(self):
        """Should correctly handle URLs starting with 'www'."""
        url = "www.example.com"
        self.assertEqual(normalize_url(url), "https://www.example.com")

    def test_normalize_empty_string(self):
        """Should return an empty string if the input is empty."""
        self.assertEqual(normalize_url(""), "")

    def test_normalize_none(self):
        """Should return an empty string if the input is None."""
        self.assertEqual(normalize_url(None), "")
