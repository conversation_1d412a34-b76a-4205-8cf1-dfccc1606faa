# Crawl4AI Agent Test Suite

This directory contains comprehensive tests for all crawler functionality including single URL crawling, multiple URL batch processing, link extraction, and impressum crawling.

## 📁 Test Structure

```
tests/
├── __init__.py                 # Test package initialization
├── agent/                      # Agent and workflow tests
│   ├── __init__.py
│   ├── test_langgraph_tools.py    # Tests for LangGraph tools
│   ├── test_integration.py        # End-to-end workflow tests
│   ├── test_langgraph_integration.py  # LangGraph integration tests
│   ├── test_langgraph_workflow.py     # Workflow tests
│   ├── test_commands.py               # Command parsing tests
│   ├── test_search_tools.py           # Search functionality tests
│   └── test_agent_quick.py            # Quick agent tests
├── crawler/                    # Crawler function tests
│   ├── __init__.py
│   └── test_crawler.py            # Unit tests for crawler functions
├── utils/                      # Utility function tests
│   ├── __init__.py
│   ├── test_file_utils.py          # File utility tests
│   ├── test_json_utils.py          # JSON utility tests
│   ├── test_url_utils.py           # URL utility tests
│   ├── test_validation_utils.py    # Validation utility tests
│   ├── test_preprocessing_utils.py # Preprocessing utility tests
│   ├── test_find_link_utils.py     # Link finding utility tests
│   └── test_logger.py              # Logger utility tests
└── README.md                   # This file
```

## 🧪 Test Categories

### 1. Crawler Tests (`crawler/test_crawler.py`)
Tests individual crawler functions in isolation:

- **TestCrawlWebsite**: Single website crawling
- **TestCrawlMultipleWebsites**: Multiple website batch processing
- **TestCrawlImpressum**: Single impressum extraction
- **TestCrawlMultipleImpressums**: Multiple impressum batch processing
- **TestCrawlLocalFile**: Local HTML file processing
- **TestLinkExtraction**: Link extraction and categorization
- **TestPageTitleExtraction**: Page title extraction utilities

### 2. Agent Tests (`agent/`)
Tests LangGraph agent functionality:

#### Tool Tests (`test_langgraph_tools.py`)
- **TestCrawlWebsiteTool**: Website crawling tool
- **TestCrawlWithLinksTool**: Link extraction tool
- **TestCrawlMultipleWebsitesTool**: Multiple website tool
- **TestCrawlMultipleImpressumsTool**: Multiple impressum tool
- **TestWebSearchTool**: Web search functionality
- **TestSearchAndCrawlTool**: Combined search and crawl
- **TestExtractCompanyDataTool**: Company data extraction
- **TestSummarizeContentTool**: Content summarization

#### Integration Tests (`test_integration.py`)
- **TestSingleURLWorkflow**: Complete single URL processing
- **TestMultipleURLWorkflow**: Complete multiple URL processing
- **TestIntentParsing**: Natural language command parsing
- **TestCommandOptions**: Command options and flags
- **TestErrorHandling**: Error handling and recovery
- **TestWebSearch**: Web search integration
- **TestChatFunctionality**: Chat and general queries

#### Other Agent Tests
- **test_langgraph_integration.py**: LangGraph workflow integration
- **test_langgraph_workflow.py**: Workflow state management
- **test_commands.py**: Command parsing and execution
- **test_search_tools.py**: Search functionality
- **test_agent_quick.py**: Quick agent functionality tests

### 3. Utility Tests (`utils/`)
Tests utility functions:

- **test_file_utils.py**: File operations and storage
- **test_json_utils.py**: JSON parsing and validation
- **test_url_utils.py**: URL validation and normalization
- **test_validation_utils.py**: Input validation functions
- **test_preprocessing_utils.py**: Content preprocessing
- **test_find_link_utils.py**: Link finding utilities
- **test_logger.py**: Logging functionality

## 🚀 Running Tests

### Quick Start
```bash
# Run quick functionality tests
python run_tests.py quick

# Run all tests
python run_tests.py all

# Run specific test categories
python run_tests.py unit        # Crawler and utility tests
python run_tests.py tools       # LangGraph tools tests
python run_tests.py integration # Integration workflow tests
python run_tests.py agent       # All agent-related tests
```

### Using pytest directly
```bash
# Run all tests
pytest tests/ -v

# Run specific test file
pytest tests/crawler/test_crawler.py -v

# Run specific test class
pytest tests/crawler/test_crawler.py::TestCrawlMultipleWebsites -v

# Run specific test method
pytest tests/crawler/test_crawler.py::TestCrawlWebsite::test_crawl_website_success -v

# Run with coverage
pytest tests/ --cov=crawler --cov=agent --cov-report=html
```

### Test Runner Options
```bash
# Available test types
python run_tests.py unit        # Crawler and utility tests
python run_tests.py tools       # LangGraph tools tests
python run_tests.py integration # Integration workflow tests
python run_tests.py agent       # All agent-related tests
python run_tests.py all         # All tests
python run_tests.py quick       # Quick subset
python run_tests.py coverage    # With coverage report
```

## 📊 Test Coverage

The test suite covers:

### Core Crawler Functions
- ✅ Single URL crawling (`crawl_website`)
- ✅ Multiple URL crawling (`crawl_multiple_websites`)
- ✅ Single impressum extraction (`crawl_impressum`)
- ✅ Multiple impressum extraction (`crawl_multiple_impressums`)
- ✅ Local file processing (`crawl_local_file`)
- ✅ Link extraction and categorization
- ✅ Page title extraction

### LangGraph Tools
- ✅ All @tool decorated functions
- ✅ Input validation and error handling
- ✅ Result formatting and structure
- ✅ Async operation handling

### Workflow Integration
- ✅ Intent parsing and command recognition
- ✅ Multiple URL detection and processing
- ✅ State management and data flow
- ✅ Error handling and recovery
- ✅ Result formatting and output

### Edge Cases
- ✅ Invalid URLs and error handling
- ✅ Mixed valid/invalid URL lists
- ✅ Network timeouts and failures
- ✅ Empty results and fallbacks
- ✅ Various URL separator formats

## 🔧 Test Configuration

### pytest.ini
The project includes a comprehensive pytest configuration:
- Async test support with `pytest-asyncio`
- Custom markers for test categorization
- Coverage reporting configuration
- Warning filters for cleaner output

### Test Markers
```python
@pytest.mark.unit          # Unit tests
@pytest.mark.integration   # Integration tests
@pytest.mark.tools         # Tool tests
@pytest.mark.slow          # Long-running tests
@pytest.mark.network       # Tests requiring network access
@pytest.mark.ollama        # Tests requiring Ollama
```

## 🐛 Debugging Tests

### Verbose Output
```bash
pytest tests/ -v -s  # Show print statements
```

### Specific Test Debugging
```bash
pytest tests/test_crawler.py::TestCrawlWebsite::test_crawl_website_success -v -s --tb=long
```

### Coverage Reports
```bash
pytest tests/ --cov=crawler --cov=agent --cov-report=html
# Open htmlcov/index.html in browser
```

## 📝 Writing New Tests

### Test Structure
```python
import pytest
from crawler.crawler import your_function

class TestYourFunction:
    @pytest.mark.asyncio
    async def test_your_function_success(self):
        """Test successful operation."""
        result = await your_function("test_input")
        assert result is not None
        assert isinstance(result, expected_type)
        
    @pytest.mark.asyncio
    async def test_your_function_error(self):
        """Test error handling."""
        with pytest.raises(ExpectedException):
            await your_function("invalid_input")
```

### Best Practices
1. **Use descriptive test names** that explain what is being tested
2. **Test both success and failure cases**
3. **Use appropriate pytest markers** for categorization
4. **Mock external dependencies** when appropriate
5. **Clean up resources** after tests (files, connections)
6. **Use fixtures** for common test setup
7. **Test edge cases** and boundary conditions

## 🎯 Test Results

Expected test outcomes:
- **Unit tests**: Should pass consistently with stable internet
- **Tool tests**: May depend on Ollama availability for some tests
- **Integration tests**: Full workflow validation
- **Network tests**: May fail with poor connectivity

## 📈 Continuous Integration

The test suite is designed to work in CI/CD environments:
- All tests are deterministic where possible
- Network-dependent tests handle failures gracefully
- Coverage reports can be generated automatically
- Test results are clearly categorized and reported
