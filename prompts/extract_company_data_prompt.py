# Double the curly braces in the template to escape them
PROMPT_EXTRACT_COMPANY_DATA_DE = """
You are a specialized data extraction assistant. Your task is to extract company information from the German Impressum/Imprint content below.

Your response MUST be a single JSON object. Do not add any text before or after the JSON.

IMPORTANT RULES:
1. Extract ONLY explicitly stated information. Do not make assumptions or inferences.
2. If a value cannot be found in the content, use "Not available".
3. Preserve the exact original formatting of numbers, addresses, and names.
4. For addresses, only combine elements that appear together in the text.
5. Look for German terms like:
   - "Geschäftsführer", "Inhaber", "Vertreten durch", "Vertretungsberechtigter" (managing director/owner)
   - "Handelsregister", "Registergericht" (commercial register)
   - "Registernummer", "HRB", "HRA" (registration number)
   - "Steuernummer", "Steuer-Nr." (tax ID)
   - "USt-IdNr.", "Umsatzsteuer-ID", "USt-ID", "Ust-Identifikationsnummer" (VAT ID)
   - "GmbH", "AG", "e.K.", "KG", "OHG", "UG", "GbR" (legal structure)
   - "Anschrift", "Adresse", "Sitz" (address)
6. BE THOROUGH - scan the entire text carefully for each piece of information.
7. If you find a company name, even without a legal structure suffix, extract it.
8. For email addresses, look for @ symbols or text like "E-Mail:" followed by an address.
9. For phone numbers, look for patterns like "+49", "Tel:", "Telefon:", followed by numbers.
10. For websites, look for "www", "http", or "Website:" followed by a domain.

EXAMPLES:
- "Weber Büro GmbH" → company_name: "Weber Büro", legal_structure: "GmbH"
- "Inhaber: Max Mustermann" → managing_director: "Max Mustermann"
- "Handelsregister: Amtsgericht Berlin HRB 12345" → register_court: "Amtsgericht Berlin", registration_number: "HRB 12345"
- "USt-IdNr.: DE123456789" → vat_id: "DE123456789"

JSON structure to fill:
```json
{{
    "company_name": "...",
    "legal_structure": "...",
    "street_address": "...",
    "zip_code": "...",
    "city": "...",
    "country": "...",
    "phone": "...",
    "email": "...",
    "website": "...",
    "managing_director": "...",
    "registration_number": "...",
    "register_court": "...",
    "tax_id": "...",
    "vat_id": "..."
}}
```

CONTENT TO PROCESS:
---
{content}
---
"""

PROMPT_EXTRACT_COMPANY_DATA_EN = """
You are a specialized data extraction assistant. Your task is to extract company information from the English Legal Notice/Imprint content below.

Your response MUST be a single JSON object. Do not add any text before or after the JSON.

IMPORTANT RULES:
1. Extract ONLY explicitly stated information. Do not make assumptions or inferences.
2. If a value cannot be found in the content, use "Not available".
3. Preserve the exact original formatting of numbers, addresses, and names.
4. For addresses, only combine elements that appear together in the text.
5. Look for English terms like:
   - "Director", "CEO", "Managing Director", "Owner", "Represented by" (managing director/owner)
   - "Company Registration", "Registered Number", "Companies House" (registration details)
   - "Company Number", "Registration Number", "Reg. No." (registration number)
   - "Tax ID", "Tax Number", "Tax Identification" (tax ID)
   - "VAT ID", "VAT Number", "VAT Registration" (VAT ID)
   - "Ltd", "Limited", "Inc", "LLC", "LLP", "PLC" (legal structure)
   - "Address", "Registered Office", "Headquarters" (address)
6. BE THOROUGH - scan the entire text carefully for each piece of information.
7. If you find a company name, even without a legal structure suffix, extract it.
8. For email addresses, look for @ symbols or text like "Email:" followed by an address.
9. For phone numbers, look for patterns like "+44", "Tel:", "Phone:", followed by numbers.
10. For websites, look for "www", "http", or "Website:" followed by a domain.

EXAMPLES:
- "Weber Office Ltd" → company_name: "Weber Office", legal_structure: "Ltd"
- "Director: John Smith" → managing_director: "John Smith"
- "Registered at Companies House: 12345678" → registration_number: "12345678"
- "VAT Registration: GB123456789" → vat_id: "GB123456789"

JSON structure to fill:
```json
{{
    "company_name": "...",
    "legal_structure": "...",
    "street_address": "...",
    "zip_code": "...",
    "city": "...",
    "country": "...",
    "phone": "...",
    "email": "...",
    "website": "...",
    "managing_director": "...",
    "registration_number": "...",
    "register_court": "...",
    "tax_id": "...",
    "vat_id": "..."
}}
```

CONTENT TO PROCESS:
---
{content}
---
"""

# TLD to prompt mapping
TLD_PROMPT_MAPPING = {
    "de": PROMPT_EXTRACT_COMPANY_DATA_DE,
    "at": PROMPT_EXTRACT_COMPANY_DATA_DE,
    "ch": PROMPT_EXTRACT_COMPANY_DATA_DE,
    "nl": PROMPT_EXTRACT_COMPANY_DATA_EN,
    "fr": PROMPT_EXTRACT_COMPANY_DATA_EN,
    "com": PROMPT_EXTRACT_COMPANY_DATA_EN,
    "co.uk": PROMPT_EXTRACT_COMPANY_DATA_EN,
    "uk": PROMPT_EXTRACT_COMPANY_DATA_EN,
    "default": PROMPT_EXTRACT_COMPANY_DATA_EN,
}


def build_extract_company_data_prompt(impressum_text: str, tld: str) -> str:
    """
    Build prompt for extracting company data from impressum/legal notice page based on TLD.

    Args:
        impressum_text (str): Content of the impressum/legal notice page
        tld (str): Top-level domain of the website (e.g., 'de', 'com', 'co.uk')

    Returns:
        str: Filled prompt template for company data extraction
    """
    tld = tld.lower().strip(".")
    prompt_template = TLD_PROMPT_MAPPING.get(tld, TLD_PROMPT_MAPPING["default"])
    return prompt_template.format(content=impressum_text)
