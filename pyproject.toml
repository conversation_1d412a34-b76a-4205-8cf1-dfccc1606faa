[tool.semantic_release]
# We don't want to publish to PyPI, so we disable the build and upload commands.
build_command = "echo 'No build step required'"
upload_to_pypi = false

# The tool will look for the version in this file.
version_source = "file"
version_variable = "crawler/__init__.py:__version__"

# The branch to create releases from.
branch = "main"

# The changelog file to update.
changelog_file = "CHANGELOG.md"