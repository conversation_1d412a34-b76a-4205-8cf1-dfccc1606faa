"""
Historical Data Importer for Crawl Statistics

This module imports historical crawl data from existing files and logs
to populate the statistics database with past crawling activity.
"""

import os
import re
import json
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Set
from urllib.parse import urlparse
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

from .database import StatisticsDatabase
from .models import CrawlRecord

logger = logging.getLogger(__name__)


class HistoricalDataImporter:
    """Import historical crawl data from existing files and logs."""

    def __init__(self, project_root: str = None, max_workers: int = 4):
        """Initialize the importer.

        Args:
            project_root: Root directory of the project. If None, uses current working directory.
            max_workers: Maximum number of worker threads for parallel processing.
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.database = StatisticsDatabase()
        self.max_workers = max_workers

        # Define directory mappings
        self.directories = {
            'results': self.project_root / 'results',
            'scraped_html': self.project_root / 'scraped_html',
            'screenshots': self.project_root / 'screenshots',
            'pdfs': self.project_root / 'pdfs',
            'logs': self.project_root / 'logs'
        }

        # Compiled regex patterns for better performance
        self.timestamp_patterns = {
            'log_files': re.compile(r'agent_(\d{8}_\d{6})\.log'),
            'timestamped_files': re.compile(r'.*_(\d{8}_\d{6})\.(json|html|png|pdf)$'),
            'date_folders': re.compile(r'(\d{4}-\d{2}-\d{2})'),
            'datetime_folders': re.compile(r'(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})')
        }

        # Cache for processed domains to avoid duplicates
        self._processed_domains: Set[str] = set()

    def import_all_historical_data(self, dry_run: bool = False) -> Dict[str, int]:
        """Import all available historical data.

        Args:
            dry_run: If True, only analyze what would be imported without actually importing

        Returns:
            Dictionary with counts of imported records by source
        """
        logger.info("Starting historical data import...")

        results = {
            'logs_processed': 0,
            'crawl_records_imported': 0,
            'domains_discovered': 0,
            'errors': 0
        }

        try:
            # Import from log files
            log_results = self._import_from_logs(dry_run)
            results.update(log_results)

            # Import from file structure
            file_results = self._import_from_file_structure(dry_run)
            results['crawl_records_imported'] += file_results.get(
                'records_imported', 0)
            results['domains_discovered'] += file_results.get(
                'domains_discovered', 0)

            # Import from result files
            result_files = self._import_from_result_files(dry_run)
            results['crawl_records_imported'] += result_files.get(
                'records_imported', 0)

            logger.info(f"Historical import completed: {results}")

        except Exception as e:
            logger.error(f"Error during historical import: {e}", exc_info=True)
            results['errors'] += 1

        return results

    def _import_from_logs(self, dry_run: bool = False) -> Dict[str, int]:
        """Import crawl data from log files."""
        results = {'logs_processed': 0, 'crawl_records_imported': 0}

        logs_dir = self.directories['logs']
        if not logs_dir.exists():
            logger.warning(f"Logs directory not found: {logs_dir}")
            return results

        log_files = list(logs_dir.glob('agent_*.log'))
        logger.info(f"Found {len(log_files)} log files to process")

        for log_file in log_files:
            try:
                # Extract timestamp from filename
                match = re.search(
                    self.timestamp_patterns['log_files'], log_file.name)
                if not match:
                    continue

                timestamp_str = match.group(1)
                log_timestamp = datetime.strptime(
                    timestamp_str, '%Y%m%d_%H%M%S')

                # Parse log file for crawl activities
                crawl_records = self._parse_log_file(log_file, log_timestamp)

                if not dry_run:
                    for record in crawl_records:
                        self.database.record_crawl(record)

                results['logs_processed'] += 1
                results['crawl_records_imported'] += len(crawl_records)

                logger.debug(
                    f"Processed {log_file.name}: {len(crawl_records)} records")

            except Exception as e:
                logger.error(f"Error processing log file {log_file}: {e}")

        return results

    def _parse_log_file(self, log_file: Path, base_timestamp: datetime) -> List[CrawlRecord]:
        """Parse a log file to extract crawl records."""
        records = []

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Look for crawl-related log entries
            crawl_patterns = [
                r'Processing request: (.+)',
                r'Crawling URL: (.+)',
                r'Successfully crawled: (.+)',
                r'Error crawling (.+): (.+)',
                r'Summarizing website: (.+)',
                r'Extracting impressum from: (.+)'
            ]

            for pattern in crawl_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    url = self._extract_url_from_log_entry(match.group(1))
                    if url:
                        record = self._create_crawl_record_from_log(
                            url, base_timestamp, match.group(0)
                        )
                        if record:
                            records.append(record)

        except Exception as e:
            logger.error(f"Error parsing log file {log_file}: {e}")

        return records

    def _import_from_file_structure(self, dry_run: bool = False) -> Dict[str, int]:
        """Import data based on file structure and timestamps."""
        results = {'records_imported': 0, 'domains_discovered': 0}
        domains_found = set()

        for dir_type, directory in self.directories.items():
            if dir_type == 'logs' or not directory.exists():
                continue

            logger.info(f"Processing {dir_type} directory: {directory}")

            # Get all domain directories
            domain_dirs = [d for d in directory.iterdir() if d.is_dir()]

            for domain_dir in domain_dirs:
                domain = domain_dir.name
                domains_found.add(domain)

                # Get all files in domain directory
                files = list(domain_dir.rglob('*'))
                files = [f for f in files if f.is_file()]

                for file_path in files:
                    try:
                        # Extract timestamp from filename or use file modification time
                        timestamp = self._extract_timestamp_from_file(
                            str(file_path))
                        if not timestamp:
                            timestamp = datetime.fromtimestamp(
                                file_path.stat().st_mtime)

                        # Create crawl record
                        record = self._create_crawl_record_from_file(
                            domain, file_path, timestamp, dir_type
                        )

                        if record:
                            if not dry_run:
                                self.database.record_crawl(record)
                            results['records_imported'] += 1

                    except Exception as e:
                        logger.error(f"Error processing file {file_path}: {e}")

        results['domains_discovered'] = len(domains_found)
        return results

    def _import_from_result_files(self, dry_run: bool = False) -> Dict[str, int]:
        """Import detailed data from result JSON files."""
        results = {'records_imported': 0}

        results_dir = self.directories['results']
        if not results_dir.exists():
            return results

        # Find all JSON result files
        json_files = list(results_dir.rglob('*.json'))

        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Extract detailed information from JSON
                record = self._create_detailed_record_from_json(
                    json_file, data)

                if record and not dry_run:
                    self.database.record_crawl(record)
                    results['records_imported'] += 1

            except Exception as e:
                logger.error(f"Error processing JSON file {json_file}: {e}")

        return results

    @lru_cache(maxsize=1000)
    def _extract_timestamp_from_file(self, file_path: str) -> Optional[datetime]:
        """Extract timestamp from filename or path (cached for performance)."""
        # Try different timestamp patterns
        for pattern in self.timestamp_patterns.values():
            match = pattern.search(file_path)
            if match:
                timestamp_str = match.group(1)
                try:
                    # Try different timestamp formats
                    formats = [
                        '%Y%m%d_%H%M%S',
                        '%Y-%m-%d',
                        '%Y-%m-%d_%H-%M-%S'
                    ]

                    for fmt in formats:
                        try:
                            return datetime.strptime(timestamp_str, fmt)
                        except ValueError:
                            continue

                except ValueError:
                    continue

        return None

    @lru_cache(maxsize=500)
    def _extract_url_from_log_entry(self, log_entry: str) -> Optional[str]:
        """Extract URL from log entry text (cached for performance)."""
        # Common URL patterns in logs (compiled for better performance)
        url_patterns = [
            re.compile(r'https?://[^\s]+'),
            re.compile(r'www\.[^\s]+'),
            re.compile(r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}')
        ]

        for pattern in url_patterns:
            match = pattern.search(log_entry)
            if match:
                url = match.group(0)
                # Clean up URL
                url = url.rstrip('.,;:)')
                if not url.startswith('http'):
                    url = f"https://{url}"
                return url

        return None

    def _create_crawl_record_from_log(self, url: str, timestamp: datetime, log_entry: str) -> Optional[CrawlRecord]:
        """Create a crawl record from log information."""
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc

            # Determine status from log entry
            status = 'success'
            if any(word in log_entry.lower() for word in ['error', 'failed', 'exception']):
                status = 'failed'

            # Estimate duration (placeholder)
            duration = 5.0  # Default duration

            return CrawlRecord(
                url=url,
                domain=domain,
                page_path=parsed_url.path or '/',
                start_time=timestamp,
                status=status,
                duration_seconds=duration,
                content_length=0,  # Unknown from logs
                error_message=log_entry if status == 'failed' else None,
                crawl_type='website'  # Use valid enum value
            )

        except Exception as e:
            logger.error(f"Error creating record from log: {e}")
            return None

    def _create_crawl_record_from_file(self, domain: str, file_path: Path,
                                       timestamp: datetime, dir_type: str) -> Optional[CrawlRecord]:
        """Create a crawl record from file information."""
        try:
            url = f"https://{domain}/"

            # Get file size as content length approximation
            content_length = file_path.stat().st_size

            # Determine crawl type from directory
            crawl_type_map = {
                'results': 'website',
                'scraped_html': 'website',
                'screenshots': 'website',
                'pdfs': 'website'
            }

            crawl_type = crawl_type_map.get(dir_type, 'website')

            return CrawlRecord(
                url=url,
                domain=domain,
                page_path='/',
                start_time=timestamp,
                status='success',  # File exists, so assume success
                duration_seconds=3.0,  # Estimated
                content_length=content_length,
                crawl_type=crawl_type
            )

        except Exception as e:
            logger.error(f"Error creating record from file: {e}")
            return None

    def _create_detailed_record_from_json(self, json_file: Path, data: Dict) -> Optional[CrawlRecord]:
        """Create a detailed crawl record from JSON result file."""
        try:
            # Extract information from JSON structure
            url = data.get('url', '')
            if not url:
                # Try to infer from filename
                domain = json_file.parent.name
                url = f"https://{domain}/"

            parsed_url = urlparse(url)
            domain = parsed_url.netloc or json_file.parent.name

            # Get timestamp from file or data
            timestamp = self._extract_timestamp_from_file(str(json_file))
            if not timestamp:
                timestamp = datetime.fromtimestamp(json_file.stat().st_mtime)

            # Extract content length
            content_length = len(json.dumps(data).encode('utf-8'))

            return CrawlRecord(
                url=url,
                domain=domain,
                page_path=parsed_url.path or '/',
                start_time=timestamp,
                status='success',
                duration_seconds=4.0,  # Estimated
                content_length=content_length,
                crawl_type='website'
            )

        except Exception as e:
            logger.error(f"Error creating record from JSON: {e}")
            return None

    def get_import_preview(self) -> Dict[str, any]:
        """Get a preview of what would be imported without actually importing."""
        logger.info("Generating import preview...")

        preview = {
            'directories_found': {},
            'estimated_records': 0,
            'date_range': {'earliest': None, 'latest': None},
            'domains_found': set(),
            'file_types': {}
        }

        # Analyze each directory
        for dir_type, directory in self.directories.items():
            if not directory.exists():
                continue

            dir_info = {
                'exists': True,
                'file_count': 0,
                'domains': set()
            }

            if dir_type == 'logs':
                log_files = list(directory.glob('agent_*.log'))
                dir_info['file_count'] = len(log_files)

                # Analyze log timestamps
                for log_file in log_files:
                    match = re.search(
                        self.timestamp_patterns['log_files'], log_file.name)
                    if match:
                        timestamp = datetime.strptime(
                            match.group(1), '%Y%m%d_%H%M%S')
                        self._update_date_range(
                            preview['date_range'], timestamp)

            else:
                # Analyze domain directories
                domain_dirs = [d for d in directory.iterdir() if d.is_dir()]
                for domain_dir in domain_dirs:
                    domain = domain_dir.name
                    dir_info['domains'].add(domain)
                    preview['domains_found'].add(domain)

                    files = list(domain_dir.rglob('*'))
                    files = [f for f in files if f.is_file()]
                    dir_info['file_count'] += len(files)

                    # Analyze file types
                    for file_path in files:
                        ext = file_path.suffix.lower()
                        preview['file_types'][ext] = preview['file_types'].get(
                            ext, 0) + 1

                        # Try to extract timestamp
                        timestamp = self._extract_timestamp_from_file(
                            str(file_path))
                        if not timestamp:
                            timestamp = datetime.fromtimestamp(
                                file_path.stat().st_mtime)
                        self._update_date_range(
                            preview['date_range'], timestamp)

            preview['directories_found'][dir_type] = dir_info
            preview['estimated_records'] += dir_info['file_count']

        # Convert sets to lists for JSON serialization
        preview['domains_found'] = list(preview['domains_found'])
        for dir_info in preview['directories_found'].values():
            if 'domains' in dir_info:
                dir_info['domains'] = list(dir_info['domains'])

        return preview

    def _update_date_range(self, date_range: Dict, timestamp: datetime):
        """Update the date range with a new timestamp."""
        if date_range['earliest'] is None or timestamp < date_range['earliest']:
            date_range['earliest'] = timestamp
        if date_range['latest'] is None or timestamp > date_range['latest']:
            date_range['latest'] = timestamp


def main():
    """CLI interface for historical data import."""
    import argparse

    parser = argparse.ArgumentParser(
        description='Import historical crawl data')
    parser.add_argument('--preview', action='store_true',
                        help='Show preview of what would be imported')
    parser.add_argument('--dry-run', action='store_true',
                        help='Analyze without actually importing')
    parser.add_argument('--project-root', type=str,
                        help='Project root directory (default: current directory)')

    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    importer = HistoricalDataImporter(args.project_root)

    if args.preview:
        preview = importer.get_import_preview()
        print("\n=== HISTORICAL DATA IMPORT PREVIEW ===")
        print(f"Estimated records to import: {preview['estimated_records']}")
        print(f"Domains found: {len(preview['domains_found'])}")
        print(
            f"Date range: {preview['date_range']['earliest']} to {preview['date_range']['latest']}")
        print(f"File types: {preview['file_types']}")
        print("\nDirectories:")
        for dir_type, info in preview['directories_found'].items():
            print(
                f"  {dir_type}: {info['file_count']} files, {len(info.get('domains', []))} domains")
    else:
        results = importer.import_all_historical_data(dry_run=args.dry_run)
        print(f"\n=== IMPORT {'PREVIEW' if args.dry_run else 'RESULTS'} ===")
        for key, value in results.items():
            print(f"{key}: {value}")


if __name__ == '__main__':
    main()
