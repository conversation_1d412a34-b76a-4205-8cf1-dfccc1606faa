"""
LangGraph tools for crawl statistics analysis and reporting.

This module provides tools that can be used by the LangGraph agent
to analyze and report on crawl statistics.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from langchain_core.tools import tool

from .database import StatisticsDatabase
from .analyzer import StatisticsAnalyzer, generate_domain_report, generate_page_report, generate_time_based_report
from .models import StatisticsReport
from .historical_importer import HistoricalDataImporter

# Get logger - use try/except to handle import issues
try:
    from utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


# Input schemas for tools
class StatisticsReportInput(BaseModel):
    """Input schema for generating statistics reports"""
    days_back: int = Field(
        default=30, description="Number of days to look back for analysis", ge=1, le=365)
    top_n: int = Field(
        default=10, description="Number of top items to include", ge=1, le=50)


class DomainAnalysisInput(BaseModel):
    """Input schema for domain analysis"""
    domain: str = Field(description="Domain name to analyze")


class PageAnalysisInput(BaseModel):
    """Input schema for page analysis"""
    url: str = Field(description="Full URL to analyze")


class TimeBasedAnalysisInput(BaseModel):
    """Input schema for time-based analysis"""
    days_back: int = Field(
        default=7, description="Number of days to look back", ge=1, le=365)
    granularity: str = Field(
        default="daily", description="Time granularity: hourly, daily, weekly, monthly")


@tool("get_crawl_statistics_summary")
def get_crawl_statistics_summary(input_data: StatisticsReportInput) -> Dict[str, Any]:
    """
    Get a comprehensive summary of crawl statistics.

    Args:
        input_data: Configuration for the statistics report

    Returns:
        Dictionary containing comprehensive crawl statistics
    """
    try:
        logger.info(
            f"Generating crawl statistics summary for last {input_data.days_back} days")

        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=input_data.days_back)

        # Check if statistics tables need rebuilding
        database = StatisticsDatabase()
        try:
            domain_count = len(database.get_top_domains(1))
            page_count = len(database.get_top_pages(1))

            if domain_count == 0 and page_count == 0:
                logger.info("Statistics tables appear empty, rebuilding...")
                database.rebuild_statistics_tables()
        except Exception as rebuild_error:
            logger.warning(
                f"Could not check/rebuild statistics tables: {rebuild_error}")

        # Generate comprehensive report
        analyzer = StatisticsAnalyzer()
        report = analyzer.generate_comprehensive_report(
            start_time=start_time,
            end_time=end_time,
            top_n=input_data.top_n
        )

        # Convert to dictionary for JSON serialization
        report_dict = {
            "report_type": report.report_type,
            "generated_at": report.generated_at.isoformat(),
            "time_period": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "days": input_data.days_back
            },
            "summary": {
                "total_crawls": report.total_crawls,
                "total_domains": report.total_domains,
                "total_pages": report.total_pages,
                "success_rate": round(report.success_rate, 2),
                "avg_crawl_duration": round(report.avg_crawl_duration, 2) if report.avg_crawl_duration else None,
                "total_content_crawled": report.total_content_crawled
            },
            "top_domains": [
                {
                    "domain": domain.domain,
                    "total_crawls": domain.total_crawls,
                    "success_rate": round(domain.success_rate, 2),
                    "unique_pages": domain.unique_pages
                }
                for domain in report.top_domains[:5]
            ],
            "top_pages": [
                {
                    "url": page.url,
                    "domain": page.domain,
                    "visit_count": page.visit_count,
                    "success_rate": round(page.success_rate, 2)
                }
                for page in report.top_pages[:5]
            ],
            "common_errors": report.common_errors,
            "insights": report.insights
        }

        logger.info("Crawl statistics summary generated successfully")
        return report_dict

    except Exception as e:
        logger.error(f"Failed to generate crawl statistics summary: {e}")
        return {"error": f"Failed to generate statistics: {str(e)}"}


@tool("analyze_domain_performance")
def analyze_domain_performance(input_data: DomainAnalysisInput) -> Dict[str, Any]:
    """
    Analyze performance metrics for a specific domain.

    Args:
        input_data: Domain analysis configuration

    Returns:
        Dictionary with detailed domain performance analysis
    """
    try:
        logger.info(f"Analyzing performance for domain: {input_data.domain}")

        # Generate domain report
        report = generate_domain_report(input_data.domain)

        if "error" in report:
            return report

        # Format the response
        formatted_report = {
            "domain": report["domain"],
            "overview": {
                "total_crawls": report["total_crawls"],
                "success_rate": round(report["success_rate"], 2),
                "unique_pages": report["unique_pages"],
                "avg_duration": round(report["avg_duration"], 2) if report["avg_duration"] else None
            },
            "timeline": {
                "first_crawl": report["first_crawl"].isoformat() if report["first_crawl"] else None,
                "last_crawl": report["last_crawl"].isoformat() if report["last_crawl"] else None
            },
            "performance_trend": report.get("performance_trend", {}),
            "error_analysis": report.get("error_analysis", {}),
            "peak_hours": report.get("peak_hours", {}),
            "crawl_frequency": report.get("crawl_frequency", {})
        }

        logger.info(
            f"Domain performance analysis completed for {input_data.domain}")
        return formatted_report

    except Exception as e:
        logger.error(f"Failed to analyze domain performance: {e}")
        return {"error": f"Failed to analyze domain: {str(e)}"}


@tool("analyze_page_performance")
def analyze_page_performance(input_data: PageAnalysisInput) -> Dict[str, Any]:
    """
    Analyze performance metrics for a specific page URL.

    Args:
        input_data: Page analysis configuration

    Returns:
        Dictionary with detailed page performance analysis
    """
    try:
        logger.info(f"Analyzing performance for URL: {input_data.url}")

        # Generate page report
        report = generate_page_report(input_data.url)

        if "error" in report:
            return report

        # Format the response
        formatted_report = {
            "url": report["url"],
            "domain": report["domain"],
            "statistics": {
                "visit_count": report["visit_count"],
                "success_rate": round(report["success_rate"], 2),
                "avg_duration": round(report["avg_duration"], 2) if report["avg_duration"] else None,
                "recent_activity": report["recent_activity"]
            },
            "timeline": {
                "first_visit": report["first_visit"].isoformat() if report["first_visit"] else None,
                "last_visit": report["last_visit"].isoformat() if report["last_visit"] else None
            },
            "last_response": {
                "response_code": report["last_response_code"],
                "content_length": report["last_content_length"]
            }
        }

        logger.info(
            f"Page performance analysis completed for {input_data.url}")
        return formatted_report

    except Exception as e:
        logger.error(f"Failed to analyze page performance: {e}")
        return {"error": f"Failed to analyze page: {str(e)}"}


@tool("analyze_time_based_trends")
def analyze_time_based_trends(input_data: TimeBasedAnalysisInput) -> Dict[str, Any]:
    """
    Analyze crawl trends over time.

    Args:
        input_data: Time-based analysis configuration

    Returns:
        Dictionary with time-based trend analysis
    """
    try:
        logger.info(
            f"Analyzing time-based trends for last {input_data.days_back} days with {input_data.granularity} granularity")

        # Calculate time range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=input_data.days_back)

        # Generate time-based report
        report = generate_time_based_report(
            start_time=start_time,
            end_time=end_time,
            granularity=input_data.granularity
        )

        # Format the response
        formatted_report = {
            "analysis_period": {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "days": input_data.days_back,
                "granularity": input_data.granularity
            },
            "volume_trend": report.get("crawl_volume_trend", {}),
            "success_rate_trend": report.get("success_rate_trend", {}),
            "performance_trend": report.get("performance_trend", {}),
            "busiest_periods": report.get("busiest_periods", []),
            "domain_activity": report.get("domain_activity_patterns", {})
        }

        logger.info("Time-based trend analysis completed")
        return formatted_report

    except Exception as e:
        logger.error(f"Failed to analyze time-based trends: {e}")
        return {"error": f"Failed to analyze trends: {str(e)}"}


@tool("get_recent_crawl_activity")
def get_recent_crawl_activity(limit: int = 20) -> Dict[str, Any]:
    """
    Get recent crawl activity.

    Args:
        limit: Maximum number of recent crawls to return

    Returns:
        Dictionary with recent crawl activity
    """
    try:
        logger.info(f"Retrieving last {limit} crawl activities")

        database = StatisticsDatabase()
        recent_crawls = database.get_crawl_history(limit=limit)

        # Format the response
        activities = []
        for crawl in recent_crawls:
            activities.append({
                "url": crawl.url,
                "domain": crawl.domain,
                "crawl_type": crawl.crawl_type,
                "status": crawl.status,
                "start_time": crawl.start_time.isoformat(),
                "duration": crawl.duration_seconds,
                "content_length": crawl.content_length,
                "response_code": crawl.response_code
            })

        response = {
            "total_activities": len(activities),
            "activities": activities
        }

        logger.info(f"Retrieved {len(activities)} recent crawl activities")
        return response

    except Exception as e:
        logger.error(f"Failed to get recent crawl activity: {e}")
        return {"error": f"Failed to retrieve activity: {str(e)}"}


@tool("cleanup_old_statistics")
def cleanup_old_statistics(days_to_keep: int = 90) -> Dict[str, Any]:
    """
    Clean up old crawl statistics to manage database size.

    Args:
        days_to_keep: Number of days of statistics to keep

    Returns:
        Dictionary with cleanup results
    """
    try:
        logger.info(f"Cleaning up statistics older than {days_to_keep} days")

        database = StatisticsDatabase()
        deleted_count = database.cleanup_old_records(days_to_keep)

        response = {
            "success": True,
            "deleted_records": deleted_count,
            "days_kept": days_to_keep,
            "cleanup_time": datetime.now().isoformat()
        }

        logger.info(f"Cleanup completed: {deleted_count} records deleted")
        return response

    except Exception as e:
        logger.error(f"Failed to cleanup old statistics: {e}")
        return {"error": f"Failed to cleanup: {str(e)}"}


# Historical Data Import Tools

class HistoricalImportInput(BaseModel):
    """Input for historical data import operations."""
    dry_run: bool = Field(
        default=True, description="Whether to perform a dry run (preview only)")
    project_root: Optional[str] = Field(
        default=None, description="Project root directory path")


@tool
def preview_historical_import(input_data: HistoricalImportInput) -> Dict[str, Any]:
    """Preview what historical data would be imported from existing files.

    Analyzes existing crawl files, logs, and results to show what historical
    data is available for import into the statistics database.
    """
    try:
        logger.info("Generating historical import preview")

        importer = HistoricalDataImporter(input_data.project_root)
        preview = importer.get_import_preview()

        # Format the preview for better readability
        formatted_preview = {
            "summary": {
                "estimated_records": preview["estimated_records"],
                "domains_found": len(preview["domains_found"]),
                "file_types_found": len(preview["file_types"]),
                "date_range": {
                    "earliest": preview["date_range"]["earliest"].isoformat() if preview["date_range"]["earliest"] else None,
                    "latest": preview["date_range"]["latest"].isoformat() if preview["date_range"]["latest"] else None
                }
            },
            "directories": {},
            # Show top 10 domains
            "top_domains": preview["domains_found"][:10],
            "file_types": preview["file_types"]
        }

        # Format directory information
        for dir_type, info in preview["directories_found"].items():
            formatted_preview["directories"][dir_type] = {
                "file_count": info["file_count"],
                "domain_count": len(info.get("domains", [])),
                "exists": info["exists"]
            }

        return {
            "status": "success",
            "preview": formatted_preview,
            "message": f"Found {preview['estimated_records']} potential records across {len(preview['domains_found'])} domains"
        }

    except Exception as e:
        logger.error(f"Failed to generate import preview: {e}", exc_info=True)
        return {
            "status": "error",
            "error": f"Failed to generate import preview: {str(e)}"
        }


@tool
def rebuild_statistics_tables(input_data: Dict[str, Any]) -> Dict[str, Any]:
    """Rebuild aggregated statistics tables from crawl records.

    This tool rebuilds the domain_stats and page_stats tables from the raw
    crawl_records data. Useful when statistics get out of sync.
    """
    try:
        logger.info("Rebuilding statistics tables")

        database = StatisticsDatabase()
        result = database.rebuild_statistics_tables()

        return {
            "status": "success",
            "message": f"Statistics rebuilt: {result['domains_rebuilt']} domains, {result['pages_rebuilt']} pages",
            "result": result
        }

    except Exception as e:
        logger.error(
            f"Failed to rebuild statistics tables: {e}", exc_info=True)
        return {
            "status": "error",
            "error": f"Failed to rebuild statistics tables: {str(e)}"
        }


@tool
def import_historical_data(input_data: HistoricalImportInput) -> Dict[str, Any]:
    """Import historical crawl data from existing files and logs.

    Imports historical crawling data from:
    - Log files with timestamps
    - Result files organized by domain
    - HTML files, screenshots, and PDFs

    This populates the statistics database with historical activity.
    """
    try:
        logger.info(
            f"Starting historical data import (dry_run={input_data.dry_run})")

        importer = HistoricalDataImporter(input_data.project_root)
        results = importer.import_all_historical_data(
            dry_run=input_data.dry_run)

        action = "would be imported" if input_data.dry_run else "imported"

        return {
            "status": "success",
            "results": results,
            "message": f"Successfully {action} {results['crawl_records_imported']} crawl records from {results['logs_processed']} log files, covering {results['domains_discovered']} domains"
        }

    except Exception as e:
        logger.error(f"Failed to import historical data: {e}", exc_info=True)
        return {
            "status": "error",
            "error": f"Failed to import historical data: {str(e)}"
        }
