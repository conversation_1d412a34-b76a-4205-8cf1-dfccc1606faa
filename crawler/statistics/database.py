"""
Database manager for crawl statistics tracking.

This module provides a SQLite-based database manager for storing and retrieving
crawl statistics with proper connection management and error handling.
"""

from .models import CrawlRecord, DomainStats, PageStats, StatisticsReport, CrawlStatus, CrawlType
import os
import sqlite3
import threading
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Configure SQLite to handle datetime properly (Python 3.12+ compatibility)


def adapt_datetime(dt):
    """Adapt datetime to ISO format string for SQLite."""
    return dt.isoformat()


def convert_datetime(s):
    """Convert datetime string back to datetime (handles multiple formats)."""
    try:
        timestamp_str = s.decode('utf-8')
        # Try ISO format first
        if 'T' in timestamp_str:
            return datetime.fromisoformat(timestamp_str)
        # Try standard SQLite format
        else:
            return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
    except (ValueError, AttributeError):
        # Fallback: try to parse as ISO format
        try:
            return datetime.fromisoformat(timestamp_str)
        except:
            # Last resort: return current time
            return datetime.now()


# Register adapters and converters
sqlite3.register_adapter(datetime, adapt_datetime)
sqlite3.register_converter("DATETIME", convert_datetime)


# Get logger - use try/except to handle import issues
try:
    from utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class StatisticsDatabase:
    """
    SQLite database manager for crawl statistics.

    Handles database connections, schema initialization, and CRUD operations
    for crawl statistics data with proper connection pooling and thread safety.
    """

    # Default database location
    DEFAULT_DB_PATH = "stats/crawl_statistics.db"

    # SQL schema file path
    SCHEMA_FILE = os.path.join(os.path.dirname(__file__), "schema.sql")

    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the database manager.

        Args:
            db_path: Path to SQLite database file (default: stats/crawl_statistics.db)
        """
        self.db_path = db_path or self.DEFAULT_DB_PATH
        self._connection_lock = threading.RLock()
        self._connections = {}  # Thread-local connections

        # Ensure directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Initialize database schema
        self._init_database()

    def _parse_datetime(self, dt_str: str) -> Optional[datetime]:
        """Parse datetime string from database."""
        if not dt_str:
            return None
        try:
            # Try ISO format first
            if 'T' in dt_str:
                return datetime.fromisoformat(dt_str)
            # Try standard SQLite format
            else:
                return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
        except (ValueError, AttributeError):
            try:
                return datetime.fromisoformat(dt_str)
            except:
                return None

    def _get_connection(self) -> sqlite3.Connection:
        """
        Get a thread-local database connection.

        Returns:
            SQLite connection object
        """
        thread_id = threading.get_ident()

        with self._connection_lock:
            if thread_id not in self._connections:
                # Create new connection for this thread
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=30.0
                )

                # Optimize SQLite settings for better performance
                optimizations = [
                    "PRAGMA foreign_keys = ON",
                    "PRAGMA journal_mode = WAL",
                    "PRAGMA synchronous = NORMAL",
                    "PRAGMA cache_size = -32000",  # 32MB cache
                    "PRAGMA temp_store = MEMORY",
                    "PRAGMA mmap_size = 134217728",  # 128MB memory map
                    "PRAGMA page_size = 4096"
                ]

                for pragma in optimizations:
                    conn.execute(pragma)

                # Return rows as dictionaries
                conn.row_factory = sqlite3.Row
                self._connections[thread_id] = conn

            return self._connections[thread_id]

    def _init_database(self) -> None:
        """Initialize the database schema if it doesn't exist."""
        try:
            # Read schema from file
            with open(self.SCHEMA_FILE, 'r') as f:
                schema_sql = f.read()

            # Execute schema creation
            with self._get_connection() as conn:
                conn.executescript(schema_sql)
                conn.commit()

            logger.info(f"Statistics database initialized at {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to initialize statistics database: {e}")
            raise

    def close(self) -> None:
        """Close all database connections."""
        with self._connection_lock:
            for thread_id, conn in list(self._connections.items()):
                try:
                    conn.close()
                    del self._connections[thread_id]
                except Exception as e:
                    logger.warning(f"Error closing database connection: {e}")

    def record_crawl(self, crawl_record: CrawlRecord) -> int:
        """
        Record a new crawl operation.

        Args:
            crawl_record: CrawlRecord object with crawl details

        Returns:
            ID of the inserted record
        """
        try:
            # Calculate duration if end_time is available
            if crawl_record.end_time and crawl_record.start_time:
                crawl_record.duration_seconds = (
                    crawl_record.end_time - crawl_record.start_time
                ).total_seconds()

            # Set created_at if not provided
            if not crawl_record.created_at:
                crawl_record.created_at = datetime.now()

            # Insert record
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO crawl_records (
                        url, domain, page_path, crawl_type, status,
                        start_time, end_time, duration_seconds,
                        content_length, response_code, error_message, user_agent,
                        created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        crawl_record.url,
                        crawl_record.domain,
                        crawl_record.page_path,
                        crawl_record.crawl_type,
                        crawl_record.status,
                        crawl_record.start_time,
                        crawl_record.end_time,
                        crawl_record.duration_seconds,
                        crawl_record.content_length,
                        crawl_record.response_code,
                        crawl_record.error_message,
                        crawl_record.user_agent,
                        crawl_record.created_at
                    )
                )
                record_id = cursor.lastrowid
                conn.commit()

                logger.debug(
                    f"Recorded crawl for {crawl_record.url} with ID {record_id}")
                return record_id

        except Exception as e:
            logger.error(f"Failed to record crawl: {e}")
            raise

    def get_crawl_record(self, record_id: int) -> Optional[CrawlRecord]:
        """
        Retrieve a crawl record by ID.

        Args:
            record_id: ID of the record to retrieve

        Returns:
            CrawlRecord object or None if not found
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM crawl_records WHERE id = ?",
                    (record_id,)
                )
                row = cursor.fetchone()

                if not row:
                    return None

                # Convert row to dictionary
                record_dict = dict(row)

                # Create CrawlRecord object
                return CrawlRecord(**record_dict)

        except Exception as e:
            logger.error(f"Failed to retrieve crawl record {record_id}: {e}")
            raise

    def get_domain_stats(self, domain: str) -> Optional[DomainStats]:
        """
        Get statistics for a specific domain.

        Args:
            domain: Domain name to get statistics for

        Returns:
            DomainStats object or None if not found
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM domain_stats WHERE domain = ?",
                    (domain,)
                )
                row = cursor.fetchone()

                if not row:
                    return None

                # Convert row to dictionary
                stats_dict = dict(row)

                # Create DomainStats object
                return DomainStats(**stats_dict)

        except Exception as e:
            logger.error(f"Failed to retrieve domain stats for {domain}: {e}")
            raise

    def get_page_stats(self, url: str) -> Optional[PageStats]:
        """
        Get statistics for a specific page URL.

        Args:
            url: URL to get statistics for

        Returns:
            PageStats object or None if not found
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM page_stats WHERE url = ?",
                    (url,)
                )
                row = cursor.fetchone()

                if not row:
                    return None

                # Convert row to dictionary
                stats_dict = dict(row)

                # Create PageStats object
                return PageStats(**stats_dict)

        except Exception as e:
            logger.error(f"Failed to retrieve page stats for {url}: {e}")
            raise

    def get_top_domains(self, limit: int = 10) -> List[DomainStats]:
        """
        Get the most frequently crawled domains.

        Args:
            limit: Maximum number of domains to return

        Returns:
            List of DomainStats objects
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT * FROM domain_stats
                    ORDER BY total_crawls DESC
                    LIMIT ?
                    """,
                    (limit,)
                )
                rows = cursor.fetchall()

                # Convert rows to DomainStats objects
                domain_stats = []
                for row in rows:
                    try:
                        row_dict = dict(row)
                        # Parse datetime fields manually
                        if 'first_crawl_time' in row_dict:
                            row_dict['first_crawl_time'] = self._parse_datetime(
                                row_dict['first_crawl_time'])
                        if 'last_crawl_time' in row_dict:
                            row_dict['last_crawl_time'] = self._parse_datetime(
                                row_dict['last_crawl_time'])
                        if 'updated_at' in row_dict:
                            row_dict['updated_at'] = self._parse_datetime(
                                row_dict['updated_at'])

                        domain_stats.append(DomainStats(**row_dict))
                    except Exception as e:
                        logger.error(
                            f"Error creating DomainStats from row {dict(row)}: {e}")
                        continue
                return domain_stats

        except Exception as e:
            logger.error(f"Failed to retrieve top domains: {e}")
            raise

    def get_top_pages(self, limit: int = 10) -> List[PageStats]:
        """
        Get the most frequently visited pages.

        Args:
            limit: Maximum number of pages to return

        Returns:
            List of PageStats objects
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT * FROM page_stats
                    ORDER BY visit_count DESC
                    LIMIT ?
                    """,
                    (limit,)
                )
                rows = cursor.fetchall()

                # Convert rows to PageStats objects
                page_stats = []
                for row in rows:
                    try:
                        row_dict = dict(row)
                        # Parse datetime fields manually
                        if 'first_visit_time' in row_dict:
                            row_dict['first_visit_time'] = self._parse_datetime(
                                row_dict['first_visit_time'])
                        if 'last_visit_time' in row_dict:
                            row_dict['last_visit_time'] = self._parse_datetime(
                                row_dict['last_visit_time'])
                        if 'updated_at' in row_dict:
                            row_dict['updated_at'] = self._parse_datetime(
                                row_dict['updated_at'])

                        page_stats.append(PageStats(**row_dict))
                    except Exception as e:
                        logger.error(
                            f"Error creating PageStats from row {dict(row)}: {e}")
                        continue
                return page_stats

        except Exception as e:
            logger.error(f"Failed to retrieve top pages: {e}")
            raise

    def get_crawl_history(
        self,
        domain: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        status: Optional[CrawlStatus] = None,
        limit: int = 100
    ) -> List[CrawlRecord]:
        """
        Get crawl history with optional filtering.

        Args:
            domain: Filter by domain (optional)
            start_time: Filter by start time (optional)
            end_time: Filter by end time (optional)
            status: Filter by status (optional)
            limit: Maximum number of records to return

        Returns:
            List of CrawlRecord objects
        """
        try:
            # Build query with filters
            query = "SELECT * FROM crawl_records WHERE 1=1"
            params = []

            if domain:
                query += " AND domain = ?"
                params.append(domain)

            if start_time:
                query += " AND start_time >= ?"
                params.append(start_time)

            if end_time:
                query += " AND start_time <= ?"
                params.append(end_time)

            if status:
                query += " AND status = ?"
                params.append(status)

            query += " ORDER BY start_time DESC LIMIT ?"
            params.append(limit)

            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()

                # Convert rows to CrawlRecord objects
                return [CrawlRecord(**dict(row)) for row in rows]

        except Exception as e:
            logger.error(f"Failed to retrieve crawl history: {e}")
            raise

    def get_summary_stats(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Get summary statistics for a time period.

        Args:
            start_time: Start of time period (optional)
            end_time: End of time period (optional)

        Returns:
            Dictionary with summary statistics
        """
        try:
            # Build query with time filters
            where_clause = "WHERE 1=1"
            params = []

            if start_time:
                where_clause += " AND start_time >= ?"
                params.append(start_time)

            if end_time:
                where_clause += " AND start_time <= ?"
                params.append(end_time)

            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Get basic counts
                cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_crawls,
                        COUNT(DISTINCT domain) as total_domains,
                        COUNT(DISTINCT url) as total_pages,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_crawls,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_crawls,
                        AVG(duration_seconds) as avg_duration,
                        COALESCE(SUM(content_length), 0) as total_content_length
                    FROM crawl_records {where_clause}
                """, params)

                row = cursor.fetchone()
                stats = dict(row) if row else {}

                # Calculate success rate
                total_crawls = stats.get('total_crawls', 0)
                successful_crawls = stats.get('successful_crawls', 0)
                stats['success_rate'] = (
                    successful_crawls / total_crawls * 100.0) if total_crawls > 0 else 0.0

                return stats

        except Exception as e:
            logger.error(f"Failed to retrieve summary statistics: {e}")
            raise

    def cleanup_old_records(self, days_to_keep: int = 90) -> int:
        """
        Clean up old crawl records to manage database size.

        Args:
            days_to_keep: Number of days of records to keep

        Returns:
            Number of records deleted
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            with self._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "DELETE FROM crawl_records WHERE created_at < ?",
                    (cutoff_date,)
                )
                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"Cleaned up {deleted_count} old crawl records")
                return deleted_count

        except Exception as e:
            logger.error(f"Failed to cleanup old records: {e}")
            raise

    def rebuild_statistics_tables(self) -> Dict[str, int]:
        """
        Rebuild domain_stats and page_stats tables from crawl_records.

        This method is useful when the aggregated statistics tables get out of sync
        with the crawl_records table.

        Returns:
            Dictionary with counts of rebuilt records
        """
        try:
            with self._get_connection() as conn:
                cursor = conn.cursor()

                # Clear existing statistics
                cursor.execute("DELETE FROM domain_stats")
                cursor.execute("DELETE FROM page_stats")

                # Rebuild domain_stats
                cursor.execute("""
                    INSERT INTO domain_stats (
                        domain, total_crawls, successful_crawls, failed_crawls,
                        first_crawl_time, last_crawl_time, unique_pages, updated_at
                    )
                    SELECT
                        domain,
                        COUNT(*) as total_crawls,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_crawls,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_crawls,
                        MIN(start_time) as first_crawl_time,
                        MAX(start_time) as last_crawl_time,
                        COUNT(DISTINCT url) as unique_pages,
                        CURRENT_TIMESTAMP as updated_at
                    FROM crawl_records
                    GROUP BY domain
                """)

                domain_count = cursor.rowcount

                # Rebuild page_stats
                cursor.execute("""
                    INSERT INTO page_stats (
                        url, domain, page_path, visit_count, successful_visits, failed_visits,
                        first_visit_time, last_visit_time, last_content_length, last_response_code, updated_at
                    )
                    SELECT
                        url,
                        domain,
                        page_path,
                        COUNT(*) as visit_count,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_visits,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_visits,
                        MIN(start_time) as first_visit_time,
                        MAX(start_time) as last_visit_time,
                        MAX(content_length) as last_content_length,
                        MAX(response_code) as last_response_code,
                        CURRENT_TIMESTAMP as updated_at
                    FROM crawl_records
                    GROUP BY url, domain, page_path
                """)

                page_count = cursor.rowcount
                conn.commit()

                logger.info(
                    f"Rebuilt statistics: {domain_count} domains, {page_count} pages")
                return {"domains_rebuilt": domain_count, "pages_rebuilt": page_count}

        except Exception as e:
            logger.error(f"Failed to rebuild statistics tables: {e}")
            raise

    def close_all_connections(self) -> None:
        """Close all database connections."""
        with self._connection_lock:
            for conn in self._connections.values():
                conn.close()
            self._connections.clear()
