"""
Crawl Statistics Tracking Module

This module provides comprehensive statistics tracking for web crawling operations,
including domain and page visit tracking, timing analysis, and reporting capabilities.
"""

__version__ = "1.0.0"

# Import main components
from .models import (
    CrawlRecord,
    DomainStats,
    PageStats,
    StatisticsReport,
    CrawlStatus,
    CrawlType
)

from .database import StatisticsDatabase

from .tracker import (
    track_crawl,
    get_statistics_tracker,
    StatisticsTracker
)

from .analyzer import (
    StatisticsAnalyzer,
    generate_domain_report,
    generate_page_report,
    generate_time_based_report
)

# Maintain clean public API
__all__ = [
    # Models
    "CrawlRecord",
    "DomainStats",
    "PageStats",
    "StatisticsReport",
    "CrawlStatus",
    "CrawlType",

    # Database
    "StatisticsDatabase",

    # Tracking
    "track_crawl",
    "get_statistics_tracker",
    "StatisticsTracker",

    # Analysis
    "StatisticsAnalyzer",
    "generate_domain_report",
    "generate_page_report",
    "generate_time_based_report"
]
