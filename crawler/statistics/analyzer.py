"""
Statistics analysis and reporting functions.

This module provides functions to analyze and report crawl statistics
including domain frequency, page visit counts, time-based analysis, and summary reports.
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics

from .models import CrawlRecord, DomainStats, PageStats, StatisticsReport, CrawlStatus, CrawlType
from .database import StatisticsDatabase

# Get logger - use try/except to handle import issues
try:
    from utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class StatisticsAnalyzer:
    """
    Analyzer class for generating comprehensive statistics reports.

    Provides methods to analyze crawl data and generate various types of reports
    including domain analysis, time-based trends, and performance metrics.
    """

    def __init__(self, database: Optional[StatisticsDatabase] = None):
        """
        Initialize the analyzer.

        Args:
            database: StatisticsDatabase instance (creates new if None)
        """
        self.database = database or StatisticsDatabase()

    def generate_comprehensive_report(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        top_n: int = 10
    ) -> StatisticsReport:
        """
        Generate a comprehensive statistics report.

        Args:
            start_time: Start of analysis period (optional)
            end_time: End of analysis period (optional)
            top_n: Number of top items to include in rankings

        Returns:
            StatisticsReport with comprehensive analysis
        """
        try:
            logger.info("Generating comprehensive statistics report")

            # Get summary statistics
            summary_stats = self.database.get_summary_stats(
                start_time, end_time)

            # Get top domains and pages
            top_domains = self.database.get_top_domains(top_n)
            top_pages = self.database.get_top_pages(top_n)

            # Get crawl history for analysis
            crawl_history = self.database.get_crawl_history(
                start_time=start_time,
                end_time=end_time,
                limit=1000  # Limit for performance
            )

            # Generate insights
            insights = self._generate_insights(crawl_history, summary_stats)

            # Analyze common errors
            common_errors = self._analyze_errors(crawl_history)

            # Create report
            report = StatisticsReport(
                report_type="comprehensive",
                time_period_start=start_time,
                time_period_end=end_time,
                total_crawls=summary_stats.get('total_crawls', 0),
                total_domains=summary_stats.get('total_domains', 0),
                total_pages=summary_stats.get('total_pages', 0),
                success_rate=summary_stats.get('success_rate', 0.0),
                top_domains=top_domains,
                top_pages=top_pages,
                avg_crawl_duration=summary_stats.get('avg_duration'),
                total_content_crawled=summary_stats.get(
                    'total_content_length', 0),
                common_errors=common_errors,
                insights=insights
            )

            logger.info(
                "Comprehensive statistics report generated successfully")
            return report

        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")
            raise

    def analyze_domain_performance(self, domain: str) -> Dict[str, Any]:
        """
        Analyze performance metrics for a specific domain.

        Args:
            domain: Domain to analyze

        Returns:
            Dictionary with domain performance analysis
        """
        try:
            # Get domain statistics
            domain_stats = self.database.get_domain_stats(domain)
            if not domain_stats:
                return {"error": f"No data found for domain: {domain}"}

            # Get crawl history for this domain
            crawl_history = self.database.get_crawl_history(
                domain=domain, limit=500)

            # Analyze performance trends
            performance_analysis = {
                "domain": domain,
                "total_crawls": domain_stats.total_crawls,
                "success_rate": domain_stats.success_rate,
                "avg_duration": domain_stats.avg_duration_seconds,
                "unique_pages": domain_stats.unique_pages,
                "first_crawl": domain_stats.first_crawl_time,
                "last_crawl": domain_stats.last_crawl_time,
                "crawl_frequency": self._calculate_crawl_frequency(crawl_history),
                "performance_trend": self._analyze_performance_trend(crawl_history),
                "error_analysis": self._analyze_domain_errors(crawl_history),
                "peak_hours": self._analyze_peak_crawl_hours(crawl_history)
            }

            return performance_analysis

        except Exception as e:
            logger.error(
                f"Failed to analyze domain performance for {domain}: {e}")
            raise

    def analyze_time_based_trends(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        granularity: str = "daily"
    ) -> Dict[str, Any]:
        """
        Analyze crawl trends over time.

        Args:
            start_time: Start of analysis period
            end_time: End of analysis period
            granularity: Time granularity ("hourly", "daily", "weekly", "monthly")

        Returns:
            Dictionary with time-based analysis
        """
        try:
            # Get crawl history
            crawl_history = self.database.get_crawl_history(
                start_time=start_time,
                end_time=end_time,
                limit=5000
            )

            # Group by time periods
            time_groups = self._group_by_time_period(
                crawl_history, granularity)

            # Calculate trends
            trends = {
                "granularity": granularity,
                "time_period_start": start_time,
                "time_period_end": end_time,
                "total_periods": len(time_groups),
                "crawl_volume_trend": self._calculate_volume_trend(time_groups),
                "success_rate_trend": self._calculate_success_rate_trend(time_groups),
                "performance_trend": self._calculate_performance_trend(time_groups),
                "busiest_periods": self._identify_busiest_periods(time_groups),
                "domain_activity_patterns": self._analyze_domain_activity_patterns(crawl_history, granularity)
            }

            return trends

        except Exception as e:
            logger.error(f"Failed to analyze time-based trends: {e}")
            raise

    def _generate_insights(self, crawl_history: List[CrawlRecord], summary_stats: Dict[str, Any]) -> List[str]:
        """Generate insights from crawl data."""
        insights = []

        try:
            total_crawls = summary_stats.get('total_crawls', 0)
            success_rate = summary_stats.get('success_rate', 0.0)
            avg_duration = summary_stats.get('avg_duration', 0.0)

            # Success rate insights
            if success_rate >= 95:
                insights.append(
                    "Excellent crawl success rate - system is performing very well")
            elif success_rate >= 85:
                insights.append(
                    "Good crawl success rate - minor issues may need attention")
            elif success_rate >= 70:
                insights.append(
                    "Moderate success rate - investigate common failure causes")
            else:
                insights.append(
                    "Low success rate - immediate attention required for crawl reliability")

            # Performance insights
            if avg_duration and avg_duration > 10:
                insights.append(
                    "Average crawl duration is high - consider performance optimization")
            elif avg_duration and avg_duration < 2:
                insights.append(
                    "Fast crawl performance - system is well optimized")

            # Volume insights
            if total_crawls > 1000:
                insights.append(
                    "High crawl volume detected - monitor system resources")
            elif total_crawls < 10:
                insights.append(
                    "Low crawl activity - system may be underutilized")

            # Domain diversity
            unique_domains = len(
                set(record.domain for record in crawl_history))
            if unique_domains > total_crawls * 0.8:
                insights.append(
                    "High domain diversity - good coverage of different websites")
            elif unique_domains < total_crawls * 0.2:
                insights.append(
                    "Low domain diversity - crawling focused on few domains")

            # Error pattern analysis
            failed_crawls = [
                r for r in crawl_history if r.status == CrawlStatus.FAILED]
            if failed_crawls:
                error_domains = Counter(r.domain for r in failed_crawls)
                if error_domains.most_common(1)[0][1] > len(failed_crawls) * 0.5:
                    insights.append(
                        f"Most errors concentrated on domain: {error_domains.most_common(1)[0][0]}")

        except Exception as e:
            logger.warning(f"Error generating insights: {e}")
            insights.append(
                "Unable to generate detailed insights due to data processing error")

        return insights

    def _analyze_errors(self, crawl_history: List[CrawlRecord]) -> Dict[str, int]:
        """Analyze common error patterns."""
        error_counter = Counter()

        for record in crawl_history:
            if record.status == CrawlStatus.FAILED and record.error_message:
                # Categorize errors
                error_msg = record.error_message.lower()
                if 'timeout' in error_msg:
                    error_counter['Timeout'] += 1
                elif 'connection' in error_msg:
                    error_counter['Connection Error'] += 1
                elif '404' in error_msg or 'not found' in error_msg:
                    error_counter['Page Not Found'] += 1
                elif '403' in error_msg or 'forbidden' in error_msg:
                    error_counter['Access Forbidden'] += 1
                elif '500' in error_msg or 'server error' in error_msg:
                    error_counter['Server Error'] += 1
                else:
                    error_counter['Other'] += 1

        return dict(error_counter.most_common(10))

    def _calculate_crawl_frequency(self, crawl_history: List[CrawlRecord]) -> Dict[str, Any]:
        """Calculate crawl frequency metrics."""
        if len(crawl_history) < 2:
            return {"frequency": "insufficient_data"}

        # Sort by time
        sorted_crawls = sorted(crawl_history, key=lambda x: x.start_time)

        # Calculate intervals
        intervals = []
        for i in range(1, len(sorted_crawls)):
            interval = (sorted_crawls[i].start_time -
                        sorted_crawls[i-1].start_time).total_seconds()
            intervals.append(interval)

        if not intervals:
            return {"frequency": "single_crawl"}

        avg_interval = statistics.mean(intervals)
        median_interval = statistics.median(intervals)

        return {
            "avg_interval_seconds": avg_interval,
            "median_interval_seconds": median_interval,
            "avg_interval_hours": avg_interval / 3600,
            "total_timespan_hours": (sorted_crawls[-1].start_time - sorted_crawls[0].start_time).total_seconds() / 3600
        }

    def _analyze_performance_trend(self, crawl_history: List[CrawlRecord]) -> Dict[str, Any]:
        """Analyze performance trends over time."""
        if not crawl_history:
            return {"trend": "no_data"}

        # Filter successful crawls with duration data
        successful_crawls = [
            r for r in crawl_history
            if r.status == CrawlStatus.SUCCESS and r.duration_seconds is not None
        ]

        if len(successful_crawls) < 3:
            return {"trend": "insufficient_data"}

        # Sort by time and analyze trend
        sorted_crawls = sorted(successful_crawls, key=lambda x: x.start_time)
        durations = [r.duration_seconds for r in sorted_crawls]

        # Simple trend analysis (first half vs second half)
        mid_point = len(durations) // 2
        first_half_avg = statistics.mean(durations[:mid_point])
        second_half_avg = statistics.mean(durations[mid_point:])

        trend_direction = "improving" if second_half_avg < first_half_avg else "degrading"
        trend_magnitude = abs(
            second_half_avg - first_half_avg) / first_half_avg * 100

        return {
            "trend_direction": trend_direction,
            "trend_magnitude_percent": trend_magnitude,
            "first_half_avg_duration": first_half_avg,
            "second_half_avg_duration": second_half_avg,
            "overall_avg_duration": statistics.mean(durations),
            "min_duration": min(durations),
            "max_duration": max(durations)
        }

    def _analyze_domain_errors(self, crawl_history: List[CrawlRecord]) -> Dict[str, Any]:
        """Analyze error patterns for a domain."""
        failed_crawls = [
            r for r in crawl_history if r.status == CrawlStatus.FAILED]

        if not failed_crawls:
            return {"error_rate": 0.0, "common_errors": {}}

        error_rate = len(failed_crawls) / len(crawl_history) * 100
        error_messages = [
            r.error_message for r in failed_crawls if r.error_message]

        # Categorize errors
        error_categories = Counter()
        for msg in error_messages:
            msg_lower = msg.lower()
            if 'timeout' in msg_lower:
                error_categories['timeout'] += 1
            elif 'connection' in msg_lower:
                error_categories['connection'] += 1
            elif '404' in msg_lower:
                error_categories['not_found'] += 1
            elif '403' in msg_lower:
                error_categories['forbidden'] += 1
            else:
                error_categories['other'] += 1

        return {
            "error_rate": error_rate,
            "total_errors": len(failed_crawls),
            "common_errors": dict(error_categories.most_common(5))
        }

    def _analyze_peak_crawl_hours(self, crawl_history: List[CrawlRecord]) -> Dict[str, Any]:
        """Analyze peak crawl hours."""
        if not crawl_history:
            return {"peak_hours": []}

        # Group by hour of day
        hour_counts = Counter()
        for record in crawl_history:
            hour_counts[record.start_time.hour] += 1

        # Find peak hours
        peak_hours = hour_counts.most_common(5)

        return {
            "peak_hours": [{"hour": hour, "count": count} for hour, count in peak_hours],
            "total_hours_active": len(hour_counts),
            "most_active_hour": peak_hours[0][0] if peak_hours else None
        }

    def _group_by_time_period(self, crawl_history: List[CrawlRecord], granularity: str) -> Dict[str, List[CrawlRecord]]:
        """Group crawl records by time period."""
        groups = defaultdict(list)

        for record in crawl_history:
            if granularity == "hourly":
                key = record.start_time.strftime("%Y-%m-%d %H:00")
            elif granularity == "daily":
                key = record.start_time.strftime("%Y-%m-%d")
            elif granularity == "weekly":
                # Get Monday of the week
                monday = record.start_time - \
                    timedelta(days=record.start_time.weekday())
                key = monday.strftime("%Y-%m-%d")
            elif granularity == "monthly":
                key = record.start_time.strftime("%Y-%m")
            else:
                key = record.start_time.strftime(
                    "%Y-%m-%d")  # Default to daily

            groups[key].append(record)

        return dict(groups)

    def _calculate_volume_trend(self, time_groups: Dict[str, List[CrawlRecord]]) -> Dict[str, Any]:
        """Calculate crawl volume trends."""
        if not time_groups:
            return {"trend": "no_data"}

        # Sort by time period
        sorted_periods = sorted(time_groups.keys())
        volumes = [len(time_groups[period]) for period in sorted_periods]

        if len(volumes) < 2:
            return {"trend": "insufficient_data"}

        # Calculate trend
        avg_volume = statistics.mean(volumes)
        first_half = volumes[:len(volumes)//2]
        second_half = volumes[len(volumes)//2:]

        first_avg = statistics.mean(first_half)
        second_avg = statistics.mean(second_half)

        trend = "increasing" if second_avg > first_avg else "decreasing"
        change_percent = abs(second_avg - first_avg) / \
            first_avg * 100 if first_avg > 0 else 0

        return {
            "trend": trend,
            "change_percent": change_percent,
            "avg_volume": avg_volume,
            "max_volume": max(volumes),
            "min_volume": min(volumes),
            "periods_analyzed": len(volumes)
        }

    def _calculate_success_rate_trend(self, time_groups: Dict[str, List[CrawlRecord]]) -> Dict[str, Any]:
        """Calculate success rate trends over time."""
        if not time_groups:
            return {"trend": "no_data"}

        success_rates = []
        for period, records in time_groups.items():
            if records:
                successful = sum(
                    1 for r in records if r.status == CrawlStatus.SUCCESS)
                rate = successful / len(records) * 100
                success_rates.append(rate)

        if len(success_rates) < 2:
            return {"trend": "insufficient_data"}

        avg_success_rate = statistics.mean(success_rates)
        first_half = success_rates[:len(success_rates)//2]
        second_half = success_rates[len(success_rates)//2:]

        first_avg = statistics.mean(first_half)
        second_avg = statistics.mean(second_half)

        trend = "improving" if second_avg > first_avg else "declining"

        return {
            "trend": trend,
            "avg_success_rate": avg_success_rate,
            "first_half_avg": first_avg,
            "second_half_avg": second_avg,
            "best_period_rate": max(success_rates),
            "worst_period_rate": min(success_rates)
        }

    def _calculate_performance_trend(self, time_groups: Dict[str, List[CrawlRecord]]) -> Dict[str, Any]:
        """Calculate performance trends over time."""
        if not time_groups:
            return {"trend": "no_data"}

        avg_durations = []
        for period, records in time_groups.items():
            successful_with_duration = [
                r for r in records
                if r.status == CrawlStatus.SUCCESS and r.duration_seconds is not None
            ]
            if successful_with_duration:
                avg_duration = statistics.mean(
                    r.duration_seconds for r in successful_with_duration)
                avg_durations.append(avg_duration)

        if len(avg_durations) < 2:
            return {"trend": "insufficient_data"}

        first_half = avg_durations[:len(avg_durations)//2]
        second_half = avg_durations[len(avg_durations)//2:]

        first_avg = statistics.mean(first_half)
        second_avg = statistics.mean(second_half)

        trend = "improving" if second_avg < first_avg else "degrading"

        return {
            "trend": trend,
            "overall_avg_duration": statistics.mean(avg_durations),
            "first_half_avg": first_avg,
            "second_half_avg": second_avg,
            "best_period_duration": min(avg_durations),
            "worst_period_duration": max(avg_durations)
        }

    def _identify_busiest_periods(self, time_groups: Dict[str, List[CrawlRecord]]) -> List[Dict[str, Any]]:
        """Identify the busiest time periods."""
        period_volumes = [(period, len(records))
                          for period, records in time_groups.items()]
        period_volumes.sort(key=lambda x: x[1], reverse=True)

        return [
            {"period": period, "crawl_count": count}
            for period, count in period_volumes[:5]
        ]

    def _analyze_domain_activity_patterns(self, crawl_history: List[CrawlRecord], granularity: str) -> Dict[str, Any]:
        """Analyze domain activity patterns over time."""
        domain_activity = defaultdict(lambda: defaultdict(int))

        for record in crawl_history:
            if granularity == "hourly":
                time_key = record.start_time.strftime("%Y-%m-%d %H:00")
            elif granularity == "daily":
                time_key = record.start_time.strftime("%Y-%m-%d")
            else:
                time_key = record.start_time.strftime("%Y-%m-%d")

            domain_activity[record.domain][time_key] += 1

        # Find most active domains
        domain_totals = {
            domain: sum(periods.values())
            for domain, periods in domain_activity.items()
        }

        top_domains = sorted(domain_totals.items(),
                             key=lambda x: x[1], reverse=True)[:5]

        return {
            "top_active_domains": [
                {"domain": domain, "total_crawls": count}
                for domain, count in top_domains
            ],
            "total_domains_active": len(domain_activity),
            "activity_distribution": dict(domain_activity)
        }


# Convenience functions for direct use
def generate_domain_report(domain: str, database: Optional[StatisticsDatabase] = None) -> Dict[str, Any]:
    """
    Generate a performance report for a specific domain.

    Args:
        domain: Domain to analyze
        database: StatisticsDatabase instance (optional)

    Returns:
        Dictionary with domain analysis
    """
    analyzer = StatisticsAnalyzer(database)
    return analyzer.analyze_domain_performance(domain)


def generate_page_report(url: str, database: Optional[StatisticsDatabase] = None) -> Dict[str, Any]:
    """
    Generate a report for a specific page URL.

    Args:
        url: URL to analyze
        database: StatisticsDatabase instance (optional)

    Returns:
        Dictionary with page analysis
    """
    try:
        db = database or StatisticsDatabase()
        page_stats = db.get_page_stats(url)

        if not page_stats:
            return {"error": f"No data found for URL: {url}"}

        # Get recent crawl history for this URL
        crawl_history = db.get_crawl_history(limit=100)
        url_history = [r for r in crawl_history if r.url == url]

        return {
            "url": url,
            "domain": page_stats.domain,
            "visit_count": page_stats.visit_count,
            "success_rate": page_stats.success_rate,
            "avg_duration": page_stats.avg_duration_seconds,
            "first_visit": page_stats.first_visit_time,
            "last_visit": page_stats.last_visit_time,
            "last_response_code": page_stats.last_response_code,
            "last_content_length": page_stats.last_content_length,
            "recent_activity": len(url_history)
        }

    except Exception as e:
        logger.error(f"Failed to generate page report for {url}: {e}")
        return {"error": f"Failed to generate report: {str(e)}"}


def generate_time_based_report(
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    granularity: str = "daily",
    database: Optional[StatisticsDatabase] = None
) -> Dict[str, Any]:
    """
    Generate a time-based analysis report.

    Args:
        start_time: Start of analysis period
        end_time: End of analysis period
        granularity: Time granularity ("hourly", "daily", "weekly", "monthly")
        database: StatisticsDatabase instance (optional)

    Returns:
        Dictionary with time-based analysis
    """
    analyzer = StatisticsAnalyzer(database)
    return analyzer.analyze_time_based_trends(start_time, end_time, granularity)
