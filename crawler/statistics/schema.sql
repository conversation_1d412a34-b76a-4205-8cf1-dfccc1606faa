-- Crawl Statistics Database Schema
-- SQLite database schema for tracking web crawling statistics

-- Main crawl records table - stores individual crawl operations
CREATE TABLE IF NOT EXISTS crawl_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT NOT NULL,
    domain TEXT NOT NULL,
    page_path TEXT NOT NULL,
    crawl_type TEXT NOT NULL CHECK (crawl_type IN ('website', 'impressum', 'smart', 'sitemap', 'local_file')),
    status TEXT NOT NULL CHECK (status IN ('success', 'failed', 'partial')),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    duration_seconds REAL,
    content_length INTEGER,
    response_code INTEGER,
    error_message TEXT,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Domain statistics aggregation table
CREATE TABLE IF NOT EXISTS domain_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    domain TEXT UNIQUE NOT NULL,
    total_crawls INTEGER DEFAULT 0,
    successful_crawls INTEGER DEFAULT 0,
    failed_crawls INTEGER DEFAULT 0,
    first_crawl_time TIMESTAMP,
    last_crawl_time TIMESTAMP,
    avg_duration_seconds REAL,
    total_content_length INTEGER DEFAULT 0,
    unique_pages INTEGER DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Page-specific statistics table
CREATE TABLE IF NOT EXISTS page_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT UNIQUE NOT NULL,
    domain TEXT NOT NULL,
    page_path TEXT NOT NULL,
    visit_count INTEGER DEFAULT 0,
    successful_visits INTEGER DEFAULT 0,
    failed_visits INTEGER DEFAULT 0,
    first_visit_time TIMESTAMP,
    last_visit_time TIMESTAMP,
    avg_duration_seconds REAL,
    last_content_length INTEGER,
    last_response_code INTEGER,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Crawl session tracking for batch operations
CREATE TABLE IF NOT EXISTS crawl_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT UNIQUE NOT NULL,
    session_type TEXT NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    total_urls INTEGER DEFAULT 0,
    successful_urls INTEGER DEFAULT 0,
    failed_urls INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Error tracking table for detailed error analysis
CREATE TABLE IF NOT EXISTS crawl_errors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    crawl_record_id INTEGER,
    error_type TEXT NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (crawl_record_id) REFERENCES crawl_records (id)
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    crawl_record_id INTEGER,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_unit TEXT,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (crawl_record_id) REFERENCES crawl_records (id)
);

-- Create triggers to automatically update aggregated statistics
CREATE TRIGGER IF NOT EXISTS update_domain_stats_after_insert
AFTER INSERT ON crawl_records
BEGIN
    INSERT OR REPLACE INTO domain_stats (
        domain, total_crawls, successful_crawls, failed_crawls,
        first_crawl_time, last_crawl_time, unique_pages, updated_at
    )
    SELECT 
        NEW.domain,
        COALESCE(ds.total_crawls, 0) + 1,
        COALESCE(ds.successful_crawls, 0) + CASE WHEN NEW.status = 'success' THEN 1 ELSE 0 END,
        COALESCE(ds.failed_crawls, 0) + CASE WHEN NEW.status = 'failed' THEN 1 ELSE 0 END,
        COALESCE(ds.first_crawl_time, NEW.start_time),
        NEW.start_time,
        (SELECT COUNT(DISTINCT url) FROM crawl_records WHERE domain = NEW.domain),
        CURRENT_TIMESTAMP
    FROM (SELECT * FROM domain_stats WHERE domain = NEW.domain) ds;
END;

CREATE TRIGGER IF NOT EXISTS update_page_stats_after_insert
AFTER INSERT ON crawl_records
BEGIN
    INSERT OR REPLACE INTO page_stats (
        url, domain, page_path, visit_count, successful_visits, failed_visits,
        first_visit_time, last_visit_time, last_content_length, last_response_code, updated_at
    )
    SELECT 
        NEW.url,
        NEW.domain,
        NEW.page_path,
        COALESCE(ps.visit_count, 0) + 1,
        COALESCE(ps.successful_visits, 0) + CASE WHEN NEW.status = 'success' THEN 1 ELSE 0 END,
        COALESCE(ps.failed_visits, 0) + CASE WHEN NEW.status = 'failed' THEN 1 ELSE 0 END,
        COALESCE(ps.first_visit_time, NEW.start_time),
        NEW.start_time,
        NEW.content_length,
        NEW.response_code,
        CURRENT_TIMESTAMP
    FROM (SELECT * FROM page_stats WHERE url = NEW.url) ps;
END;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_crawl_records_domain ON crawl_records (domain);
CREATE INDEX IF NOT EXISTS idx_crawl_records_url ON crawl_records (url);
CREATE INDEX IF NOT EXISTS idx_crawl_records_start_time ON crawl_records (start_time);
CREATE INDEX IF NOT EXISTS idx_crawl_records_status ON crawl_records (status);
CREATE INDEX IF NOT EXISTS idx_crawl_records_crawl_type ON crawl_records (crawl_type);

CREATE INDEX IF NOT EXISTS idx_domain_stats_domain ON domain_stats (domain);
CREATE INDEX IF NOT EXISTS idx_domain_stats_last_crawl ON domain_stats (last_crawl_time);

CREATE INDEX IF NOT EXISTS idx_page_stats_url ON page_stats (url);
CREATE INDEX IF NOT EXISTS idx_page_stats_domain ON page_stats (domain);
CREATE INDEX IF NOT EXISTS idx_page_stats_last_visit ON page_stats (last_visit_time);
CREATE INDEX IF NOT EXISTS idx_page_stats_visit_count ON page_stats (visit_count);

CREATE INDEX IF NOT EXISTS idx_crawl_sessions_session_id ON crawl_sessions (session_id);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_start_time ON crawl_sessions (start_time);

CREATE INDEX IF NOT EXISTS idx_crawl_errors_type ON crawl_errors (error_type);
CREATE INDEX IF NOT EXISTS idx_crawl_errors_occurred_at ON crawl_errors (occurred_at);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics (metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recorded_at ON performance_metrics (recorded_at);
