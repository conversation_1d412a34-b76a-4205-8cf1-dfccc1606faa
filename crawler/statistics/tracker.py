"""
Statistics tracking decorator and utilities.

This module provides decorators and utilities for automatically tracking
crawl statistics without modifying the core crawling logic.
"""

import functools
import inspect
import time
from typing import Any, Callable, Dict, Optional, Union, Tuple
from datetime import datetime
from urllib.parse import urlparse

from .models import CrawlRecord, CrawlStatus, CrawlType
from .database import StatisticsDatabase

# Get logger - use try/except to handle import issues
try:
    from utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class StatisticsTracker:
    """
    Singleton class for managing statistics tracking across the application.

    Provides a centralized way to track crawl operations and manage
    the statistics database connection.
    """

    _instance = None
    _database = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(StatisticsTracker, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._database is None:
            self._database = StatisticsDatabase()

    @property
    def database(self) -> StatisticsDatabase:
        """Get the statistics database instance."""
        return self._database

    def track_crawl_operation(
        self,
        url: str,
        crawl_type: CrawlType,
        start_time: datetime,
        end_time: Optional[datetime] = None,
        status: CrawlStatus = CrawlStatus.SUCCESS,
        content_length: Optional[int] = None,
        response_code: Optional[int] = None,
        error_message: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> int:
        """
        Track a crawl operation.

        Args:
            url: URL that was crawled
            crawl_type: Type of crawl operation
            start_time: When the crawl started
            end_time: When the crawl completed (optional)
            status: Status of the crawl operation
            content_length: Size of content in bytes (optional)
            response_code: HTTP response code (optional)
            error_message: Error message if failed (optional)
            user_agent: User agent used (optional)

        Returns:
            ID of the recorded crawl operation
        """
        try:
            # Parse URL to extract domain and path
            parsed_url = urlparse(url)
            domain = parsed_url.netloc.replace('www.', '')
            page_path = parsed_url.path or '/'

            # Create crawl record
            crawl_record = CrawlRecord(
                url=url,
                domain=domain,
                page_path=page_path,
                crawl_type=crawl_type,
                status=status,
                start_time=start_time,
                end_time=end_time,
                content_length=content_length,
                response_code=response_code,
                error_message=error_message,
                user_agent=user_agent
            )

            # Record in database
            record_id = self._database.record_crawl(crawl_record)

            logger.debug(
                f"Tracked crawl operation for {url} (ID: {record_id})")
            return record_id

        except Exception as e:
            logger.error(f"Failed to track crawl operation for {url}: {e}")
            # Don't raise exception to avoid breaking the main crawl operation
            return -1


# Global tracker instance
_tracker = StatisticsTracker()


def get_statistics_tracker() -> StatisticsTracker:
    """Get the global statistics tracker instance."""
    return _tracker


def track_crawl(
    crawl_type: Optional[CrawlType] = None,
    extract_url_from_args: bool = True,
    url_arg_name: str = 'url',
    track_errors: bool = True
):
    """
    Decorator to automatically track crawl operations.

    This decorator wraps crawl functions to automatically record statistics
    about the crawl operation including timing, success/failure, and metadata.

    Args:
        crawl_type: Type of crawl operation (auto-detected if None)
        extract_url_from_args: Whether to extract URL from function arguments
        url_arg_name: Name of the URL argument in the function
        track_errors: Whether to track errors and exceptions

    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await _track_operation(
                func, args, kwargs, crawl_type, extract_url_from_args,
                url_arg_name, track_errors, is_async=True
            )

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            return _track_operation(
                func, args, kwargs, crawl_type, extract_url_from_args,
                url_arg_name, track_errors, is_async=False
            )

        # Return appropriate wrapper based on function type
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


async def _track_operation(
    func: Callable,
    args: tuple,
    kwargs: dict,
    crawl_type: Optional[CrawlType],
    extract_url_from_args: bool,
    url_arg_name: str,
    track_errors: bool,
    is_async: bool
) -> Any:
    """
    Internal function to track a crawl operation.

    Args:
        func: Function being wrapped
        args: Function positional arguments
        kwargs: Function keyword arguments
        crawl_type: Type of crawl operation
        extract_url_from_args: Whether to extract URL from arguments
        url_arg_name: Name of URL argument
        track_errors: Whether to track errors
        is_async: Whether the function is async

    Returns:
        Result of the original function
    """
    start_time = datetime.now()
    url = None
    status = CrawlStatus.SUCCESS
    error_message = None
    content_length = None
    response_code = None

    try:
        # Extract URL from function arguments
        if extract_url_from_args:
            url = _extract_url_from_args(func, args, kwargs, url_arg_name)

        # Auto-detect crawl type if not provided
        if crawl_type is None:
            crawl_type = _detect_crawl_type(func.__name__)

        # Execute the original function
        if is_async:
            result = await func(*args, **kwargs)
        else:
            result = func(*args, **kwargs)

        # Extract metadata from result if possible
        content_length, response_code = _extract_result_metadata(result)

        return result

    except Exception as e:
        status = CrawlStatus.FAILED
        error_message = str(e)

        if track_errors:
            logger.warning(f"Crawl operation failed for {url}: {e}")

        # Re-raise the exception
        raise

    finally:
        # Track the operation if we have a URL
        if url:
            end_time = datetime.now()

            try:
                _tracker.track_crawl_operation(
                    url=url,
                    crawl_type=crawl_type,
                    start_time=start_time,
                    end_time=end_time,
                    status=status,
                    content_length=content_length,
                    response_code=response_code,
                    error_message=error_message
                )
            except Exception as track_error:
                logger.error(f"Failed to track crawl operation: {track_error}")


def _extract_url_from_args(
    func: Callable,
    args: tuple,
    kwargs: dict,
    url_arg_name: str
) -> Optional[str]:
    """
    Extract URL from function arguments.

    Args:
        func: Function being called
        args: Positional arguments
        kwargs: Keyword arguments
        url_arg_name: Name of URL argument

    Returns:
        Extracted URL or None
    """
    try:
        # First check keyword arguments
        if url_arg_name in kwargs:
            return kwargs[url_arg_name]

        # Then check positional arguments
        sig = inspect.signature(func)
        param_names = list(sig.parameters.keys())

        if url_arg_name in param_names:
            param_index = param_names.index(url_arg_name)
            if param_index < len(args):
                return args[param_index]

        # Fallback: try first argument if it looks like a URL
        if args and isinstance(args[0], str) and ('http://' in args[0] or 'https://' in args[0]):
            return args[0]

        return None

    except Exception as e:
        logger.debug(f"Failed to extract URL from arguments: {e}")
        return None


def _detect_crawl_type(function_name: str) -> CrawlType:
    """
    Auto-detect crawl type from function name.

    Args:
        function_name: Name of the function being called

    Returns:
        Detected crawl type
    """
    name_lower = function_name.lower()

    if 'impressum' in name_lower or 'company' in name_lower:
        return CrawlType.IMPRESSUM
    elif 'smart' in name_lower:
        return CrawlType.SMART
    elif 'sitemap' in name_lower:
        return CrawlType.SITEMAP
    elif 'local' in name_lower or 'file' in name_lower:
        return CrawlType.LOCAL_FILE
    else:
        return CrawlType.WEBSITE


def _extract_result_metadata(result: Any) -> Tuple[Optional[int], Optional[int]]:
    """
    Extract metadata from function result.

    Args:
        result: Result from the crawl function

    Returns:
        Tuple of (content_length, response_code)
    """
    content_length = None
    response_code = None

    try:
        # Handle different result formats
        if isinstance(result, tuple):
            # Check if result contains HTML content (common pattern)
            for item in result:
                if isinstance(item, str) and item is not None and len(item) > 100:
                    content_length = len(item.encode('utf-8'))
                    break
        elif isinstance(result, str) and result is not None:
            content_length = len(result.encode('utf-8'))
        elif isinstance(result, dict):
            # Check for common response fields
            if 'content_length' in result:
                content_length = result['content_length']
            if 'response_code' in result:
                response_code = result['response_code']
            elif 'status_code' in result:
                response_code = result['status_code']

    except Exception as e:
        logger.debug(f"Failed to extract result metadata: {e}")

    return content_length, response_code
