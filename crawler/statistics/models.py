"""
Pydantic models for crawl statistics data structures.

This module defines strict data schemas for consistent JSON output
and database operations for crawl statistics tracking.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum
from datetime import datetime
from urllib.parse import urlparse


class CrawlStatus(str, Enum):
    """Enumeration of crawl operation statuses."""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"


class CrawlType(str, Enum):
    """Enumeration of crawl operation types."""
    WEBSITE = "website"
    IMPRESSUM = "impressum"
    SMART = "smart"
    SITEMAP = "sitemap"
    LOCAL_FILE = "local_file"


class CrawlRecord(BaseModel):
    """
    Model for individual crawl operation records.

    Represents a single crawl operation with all relevant metadata
    including timing, status, and performance metrics.
    """
    model_config = ConfigDict(
        validate_assignment=True,
        use_enum_values=True
    )

    # Primary identifiers
    id: Optional[int] = Field(default=None, description="Database record ID")
    url: str = Field(description="Full URL that was crawled", max_length=2048)
    domain: str = Field(
        default="", description="Domain extracted from URL", max_length=255)
    page_path: str = Field(
        default="", description="Path portion of the URL", max_length=1024)

    # Operation metadata
    crawl_type: CrawlType = Field(description="Type of crawl operation")
    status: CrawlStatus = Field(description="Status of the crawl operation")

    # Timing information
    start_time: datetime = Field(description="When the crawl started")
    end_time: Optional[datetime] = Field(
        default=None, description="When the crawl completed")
    duration_seconds: Optional[float] = Field(
        default=None, description="Duration in seconds", ge=0.0)

    # Response metadata
    content_length: Optional[int] = Field(
        default=None, description="Size of content in bytes", ge=0)
    response_code: Optional[int] = Field(
        default=None, description="HTTP response code", ge=100, le=599)

    # Error information
    error_message: Optional[str] = Field(
        default=None, description="Error message if failed", max_length=1024)

    # Technical metadata
    user_agent: Optional[str] = Field(
        default=None, description="User agent used for crawling", max_length=512)
    created_at: Optional[datetime] = Field(
        default=None, description="When record was created")

    @field_validator('url', 'domain', 'page_path')
    @classmethod
    def validate_and_strip_strings(cls, v: str) -> str:
        """Validate and strip string fields."""
        if v is None:
            return ""
        v = v.strip()
        return v

    @field_validator('url')
    @classmethod
    def validate_url(cls, v: str) -> str:
        """Validate URL format."""
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('URL must start with http:// or https://')
        return v

    @field_validator('error_message', 'user_agent')
    @classmethod
    def validate_optional_strings(cls, v: Optional[str]) -> Optional[str]:
        """Validate optional string fields."""
        if v is None:
            return None
        return v.strip() if v else None

    def model_post_init(self, __context) -> None:
        """Extract domain and page_path from URL after model initialization."""
        if self.url and not self.domain:
            parsed = urlparse(self.url)
            self.domain = parsed.netloc.replace('www.', '')

        if self.url and not self.page_path:
            parsed = urlparse(self.url)
            self.page_path = parsed.path or '/'


class DomainStats(BaseModel):
    """
    Aggregated statistics for a specific domain.

    Contains summary information about all crawl operations
    performed on a particular domain.
    """
    model_config = ConfigDict(
        validate_assignment=True
    )

    # Primary identifier
    id: Optional[int] = Field(default=None, description="Database record ID")
    domain: str = Field(description="Domain name", max_length=255)

    # Crawl counts
    total_crawls: int = Field(
        default=0, description="Total number of crawl operations", ge=0)
    successful_crawls: int = Field(
        default=0, description="Number of successful crawls", ge=0)
    failed_crawls: int = Field(
        default=0, description="Number of failed crawls", ge=0)

    # Timing information
    first_crawl_time: Optional[datetime] = Field(
        default=None, description="First crawl timestamp")
    last_crawl_time: Optional[datetime] = Field(
        default=None, description="Most recent crawl timestamp")
    avg_duration_seconds: Optional[float] = Field(
        default=None, description="Average crawl duration", ge=0.0)

    # Content statistics
    total_content_length: int = Field(
        default=0, description="Total bytes crawled", ge=0)
    unique_pages: int = Field(
        default=0, description="Number of unique pages crawled", ge=0)

    # Metadata
    updated_at: Optional[datetime] = Field(
        default=None, description="Last update timestamp")

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_crawls == 0:
            return 0.0
        return (self.successful_crawls / self.total_crawls) * 100.0


class PageStats(BaseModel):
    """
    Statistics for a specific page/URL.

    Tracks visit frequency and performance metrics for individual pages.
    """
    model_config = ConfigDict(
        validate_assignment=True
    )

    # Primary identifiers
    id: Optional[int] = Field(default=None, description="Database record ID")
    url: str = Field(description="Full URL", max_length=2048)
    domain: str = Field(description="Domain name", max_length=255)
    page_path: str = Field(description="Page path", max_length=1024)

    # Visit statistics
    visit_count: int = Field(
        default=0, description="Total number of visits", ge=0)
    successful_visits: int = Field(
        default=0, description="Number of successful visits", ge=0)
    failed_visits: int = Field(
        default=0, description="Number of failed visits", ge=0)

    # Timing information
    first_visit_time: Optional[datetime] = Field(
        default=None, description="First visit timestamp")
    last_visit_time: Optional[datetime] = Field(
        default=None, description="Most recent visit timestamp")
    avg_duration_seconds: Optional[float] = Field(
        default=None, description="Average visit duration", ge=0.0)

    # Latest response information
    last_content_length: Optional[int] = Field(
        default=None, description="Size of last response", ge=0)
    last_response_code: Optional[int] = Field(
        default=None, description="Last HTTP response code", ge=100, le=599)

    # Metadata
    updated_at: Optional[datetime] = Field(
        default=None, description="Last update timestamp")

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.visit_count == 0:
            return 0.0
        return (self.successful_visits / self.visit_count) * 100.0


class StatisticsReport(BaseModel):
    """
    Comprehensive statistics report containing various metrics and summaries.

    Used for generating detailed analysis reports of crawling activity.
    """
    model_config = ConfigDict(
        validate_assignment=True
    )

    # Report metadata
    report_type: str = Field(description="Type of report", max_length=50)
    generated_at: datetime = Field(
        default_factory=datetime.now, description="Report generation timestamp")
    time_period_start: Optional[datetime] = Field(
        default=None, description="Start of analysis period")
    time_period_end: Optional[datetime] = Field(
        default=None, description="End of analysis period")

    # Summary statistics
    total_crawls: int = Field(
        default=0, description="Total crawl operations", ge=0)
    total_domains: int = Field(
        default=0, description="Total unique domains", ge=0)
    total_pages: int = Field(default=0, description="Total unique pages", ge=0)
    success_rate: float = Field(
        default=0.0, description="Overall success rate percentage", ge=0.0, le=100.0)

    # Top statistics
    top_domains: List[DomainStats] = Field(
        default_factory=list, description="Most crawled domains")
    top_pages: List[PageStats] = Field(
        default_factory=list, description="Most visited pages")

    # Performance metrics
    avg_crawl_duration: Optional[float] = Field(
        default=None, description="Average crawl duration", ge=0.0)
    total_content_crawled: int = Field(
        default=0, description="Total bytes crawled", ge=0)

    # Error analysis
    common_errors: Dict[str, int] = Field(
        default_factory=dict, description="Common error types and counts")

    # Additional insights
    insights: List[str] = Field(
        default_factory=list, description="Generated insights and recommendations")

    # Raw data for detailed analysis
    additional_data: Dict[str, Any] = Field(
        default_factory=dict, description="Additional analysis data")
