"""
Core crawling functionality for basic website crawling operations.

This module contains the fundamental crawling functions that handle:
- Single website crawling with optional screenshots, PDFs, and link extraction
- Multiple website batch processing
- Local HTML file processing
- Page title extraction utilities
"""

import os
import base64
from typing import Dict, List, Union, Optional, Any
from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig

from utils.file_utils import save_html_to_dir, read_local_file, save_media_to_dir
from utils.logger import logger
from .utils import _extract_and_categorize_links, _extract_page_title
from .statistics import track_crawl, CrawlType


@track_crawl(crawl_type=CrawlType.WEBSITE)
async def crawl_website(
    url: str, screenshot: bool = False, pdf: bool = False, extract_links: bool = False
) -> tuple[
    str, str | None, str | None, str | None, Dict[str,
                                                  List[str]] | None, str | None
]:
    """
    Crawl a website, save its raw HTML, and return its content in markdown format.
    Optionally capture screenshots, PDFs, and extract internal/external links.

    Args:
        url (str): The URL of the website to crawl
        screenshot (bool): Whether to capture a screenshot
        pdf (bool): Whether to generate a PDF
        extract_links (bool): Whether to extract and categorize internal/external links

    Returns:
        A tuple containing:
        - Markdown content
        - Path to saved HTML file
        - Path to saved screenshot (if requested)
        - Path to saved PDF (if requested)
        - Dictionary with 'internal' and 'external' link lists (if extract_links=True)
        - Page title (if available)
    """
    # Configure browser to run in headless mode (without GUI)
    browser_config = BrowserConfig(headless=True)
    # Configure crawler to bypass cache for fresh results and capture media if requested
    # Magic Mode simulates human-like browsing to avoid bot detection
    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        screenshot=screenshot,
        pdf=pdf,
        magic=True,  # Simplifies a lot of interaction, simulates human-like browsing
        # Simulate user interactions (mouse movements) to avoid bot detection
        simulate_user=True,
        # Wait for the page to be fully loaded before proceeding
        wait_until="networkidle",  # Wait for network to be idle
        page_timeout=60000,  # 60 second timeout
        delay_before_return_html=0.5  # Small delay to ensure page is stable
    )

    # Create and use crawler within context manager for proper resource cleanup
    async with AsyncWebCrawler(config=browser_config) as crawler:
        result = await crawler.arun(url=url, config=crawler_config)

        if not result.success:
            logger.error(f"Failed to crawl {url}: {result.error_message}")
            return (
                f"Error: Could not crawl website content for {url}.",
                None,
                None,
                None,
            )

        # Save the fetched HTML content
        saved_html_path = save_html_to_dir(result.html, url)

        # Handle screenshot if requested
        screenshot_path = None
        if screenshot and result.screenshot:
            screenshot_path = save_media_to_dir(
                base64.b64decode(result.screenshot), url, "screenshots", "png"
            )
            logger.info(f"Screenshot saved to: {screenshot_path}")

        # Handle PDF if requested
        pdf_path = None
        if pdf and result.pdf:
            pdf_path = save_media_to_dir(result.pdf, url, "pdfs", "pdf")
            logger.info(f"PDF saved to: {pdf_path}")

        # Extract page title
        page_title = _extract_page_title(result)

        # Extract and categorize links if requested
        links_data = None
        if extract_links and result.links:
            links_data = _extract_and_categorize_links(url, result.links)
            logger.info(
                f"Extracted {len(links_data['internal'])} internal and {len(links_data['external'])} external links"
            )

        return (
            result.markdown,
            saved_html_path,
            screenshot_path,
            pdf_path,
            links_data,
            page_title,
        )


@track_crawl(crawl_type=CrawlType.WEBSITE, extract_url_from_args=False)
async def crawl_multiple_websites(
    urls: Union[str, List[str]],
    screenshot: bool = False,
    pdf: bool = False,
    extract_links: bool = False,
) -> List[Dict[str, any]]:
    """
    Crawl multiple websites and return their content.

    Args:
        urls: Single URL string or list of URLs to crawl
        screenshot: Whether to capture screenshots
        pdf: Whether to generate PDFs
        extract_links: Whether to extract and categorize links

    Returns:
        List of dictionaries containing crawl results for each URL
    """
    # Normalize input to list
    if isinstance(urls, str):
        url_list = [urls]
    else:
        url_list = urls

    results = []

    for i, url in enumerate(url_list, 1):
        logger.info(f"Crawling URL {i}/{len(url_list)}: {url}")

        try:
            # Crawl individual website
            result = await crawl_website(url, screenshot, pdf, extract_links)
            (
                markdown_content,
                html_path,
                screenshot_path,
                pdf_path,
                links_data,
                page_title,
            ) = result

            # Create result dictionary
            crawl_result = {
                "url": url,
                "success": True,
                "page_title": page_title,
                "markdown_content": markdown_content,
                "html_path": html_path,
                "screenshot_path": screenshot_path,
                "pdf_path": pdf_path,
                "links_data": links_data,
                "error": None,
            }

            results.append(crawl_result)
            logger.info(f"Successfully crawled {url}")

        except Exception as e:
            logger.error(f"Failed to crawl {url}: {e}")
            error_result = {
                "url": url,
                "success": False,
                "page_title": None,
                "markdown_content": None,
                "html_path": None,
                "screenshot_path": None,
                "pdf_path": None,
                "links_data": None,
                "error": str(e),
            }
            results.append(error_result)

    logger.info(
        f"Completed crawling {len(url_list)} URLs. Success: {sum(1 for r in results if r['success'])}/{len(url_list)}"
    )
    return results


@track_crawl(crawl_type=CrawlType.LOCAL_FILE, extract_url_from_args=False)
async def crawl_local_file(local_file_path: str) -> str | None:
    """
    Crawls a local HTML file and returns its content in Markdown format.

    Args:
        local_file_path (str): The path (absolute or relative) to the local HTML file.

    Returns:
        The Markdown content of the file, or None if crawling fails.
    """
    html_content = read_local_file(local_file_path)
    if html_content is None:
        return None

    file_url = f"file://{os.path.abspath(local_file_path)}"
    # Use a specific config for local files, bypassing cache is a good default.
    # Explicitly disable console capture to work around a bug in crawl4ai.
    # WORKAROUND: The library has a bug causing an UnboundLocalError when
    # console capture is False. Setting it to True avoids the error.
    # Note: file_url might not strictly be needed, as html_content is provided.
    config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS, capture_console_messages=True
    )
    # Configure a headless browser, as crawl4ai might need it for parsing.
    # browser_config = BrowserConfig(headless=True)

    async with AsyncWebCrawler() as crawler:
        # Provide the HTML content directly for more reliable processing.
        result = await crawler.arun(
            url=file_url, html_content=html_content, config=config
        )
        if result.success:
            logger.info(f"Successfully crawled local file: {local_file_path}")
            return result.markdown
        else:
            logger.error(
                f"Failed to crawl {local_file_path}: {result.error_message}")
            return None
