"""
Company Data (Legal Notice/Impressum) crawling functionality.

This module contains specialized functions for finding and extracting
company legal information from Impressum/Legal Notice pages with advanced
content filtering, bot detection avoidance, and structured data extraction.
"""

from typing import Dict, List, Union, Optional
from urllib.parse import urljoin
from crawl4ai import AsyncWebCrawler, CacheMode, BrowserConfig, CrawlerRunConfig, LLMConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from crawl4ai.content_filter_strategy import LLMContentFilter

from utils.file_utils import save_html_to_dir
from utils.find_link_utils import find_impressum_link
from utils.logger import logger
from .models import CompanyData
from .extractors import get_factory, ExtractorType
from .statistics import track_crawl, CrawlType


@track_crawl(crawl_type=CrawlType.IMPRESSUM, url_arg_name='base_url')
async def extract_company_data(base_url: str, extractor_type: ExtractorType = ExtractorType.AUTO) -> CompanyData:
    """
    Extract structured company data from impressum/legal notice pages.

    This function crawls the website to find the impressum page and then
    extracts structured company data using the specified extraction strategy.

    Args:
        base_url: The base URL of the website to crawl
        extractor_type: Type of extractor to use (AUTO, REGEX, or LLM)

    Returns:
        CompanyData instance with extracted and validated company information
    """
    # First crawl the impressum page to get HTML content
    markdown_content, html_path = await crawl_impressum(base_url)

    if not markdown_content:
        logger.warning(f"No impressum content found for {base_url}")
        return CompanyData(
            source_url=base_url,
            extraction_method="failed",
            confidence_score=0.0
        )

    # Read the HTML content from the saved file
    html_content = ""
    if html_path:
        try:
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
        except Exception as e:
            logger.warning(f"Could not read HTML file {html_path}: {e}")
            # Fallback to using markdown content as text
            html_content = f"<html><body>{markdown_content}</body></html>"
    else:
        # Fallback to using markdown content as text
        html_content = f"<html><body>{markdown_content}</body></html>"

    # Extract structured data using the factory
    factory = get_factory()

    try:
        if extractor_type == ExtractorType.AUTO:
            result = await factory.extract_with_auto_selection(html_content, base_url)
        else:
            result = await factory.extract_with_fallback(
                html_content=html_content,
                source_url=base_url,
                primary_type=extractor_type,
                fallback_type=ExtractorType.LLM if extractor_type == ExtractorType.REGEX else ExtractorType.REGEX
            )

        # Convert to CompanyData model
        company_data = result.to_company_data(base_url)

        logger.info(
            f"Company data extraction completed for {base_url}: "
            f"method={result.extraction_method}, confidence={result.confidence_score:.2f}, "
            f"valid={company_data.is_valid_extraction()}"
        )

        return company_data

    except Exception as e:
        logger.error(f"Company data extraction failed for {base_url}: {e}")
        return CompanyData(
            source_url=base_url,
            extraction_method="error",
            confidence_score=0.0,
            additional_info={"error": str(e)}
        )


@track_crawl(crawl_type=CrawlType.IMPRESSUM, url_arg_name='base_url')
async def crawl_impressum(base_url: str) -> tuple[str | None, str | None]:
    """
    Crawls the homepage to find the impressum/legal notice link, saves its HTML,
    and then returns the content of that page.

    Args:
        base_url (str): The base URL of the website to crawl

    Returns:
        A tuple containing the Markdown content and the path to the saved HTML file,
        or (None, None) if not found.
    """
    browser_config = BrowserConfig(
        headless=True,                        # No browser window
        java_script_enabled=True,             # JavaScript enabled
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        extra_args=[
            "--disable-blink-features=AutomationControlled",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-gpu",
            "--disable-features=VizDisplayCompositor",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-ipc-flooding-protection",
            "--disable-renderer-backgrounding",
            "--disable-backgrounding-occluded-windows",
            "--disable-background-timer-throttling"
        ]
    )
    # Create specialized markdown generator for impressum extraction
    impressum_md_generator = _create_impressum_markdown_generator()

    crawler_config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        exclude_external_links=False,          # No links outside primary domain
        exclude_social_media_links=False,      # Skip recognized social media domains
        magic=True,  # Simplifies a lot of interaction, simulates human-like browsing
        # Simulate user interactions (mouse movements) to avoid bot detection
        simulate_user=True,
        # Use specialized markdown generation for better content extraction
        markdown_generator=impressum_md_generator,
    )

    async with AsyncWebCrawler(config=browser_config) as crawler:
        try:
            # 1. Crawl the homepage to find the impressum link.
            homepage_result = await crawler.arun(url=base_url, config=crawler_config)
            if not homepage_result.success:
                logger.error(
                    f"Failed to crawl homepage {base_url} to find Impressum.")
                return None, None
        except Exception as e:
            logger.warning(f"Error during homepage crawl for {base_url}: {e}")
            # Try to continue with common patterns if homepage crawl fails
            homepage_result = None

        # 2. Find the link in the homepage's HTML.
        impressum_url = None
        if homepage_result and homepage_result.html:
            impressum_url = await find_impressum_link(homepage_result.html, base_url)

        if not impressum_url:
            logger.warning(
                f"No Impressum link found on {base_url}, trying common patterns")
            # Skip to trying common patterns
            impressum_result = None
        else:
            logger.info(f"Found Impressum page: {impressum_url}")

            # 3. Crawl the Impressum page itself.
            try:
                impressum_result = await crawler.arun(url=impressum_url, config=crawler_config)
            except Exception as e:
                logger.warning(
                    f"Error crawling impressum URL {impressum_url}: {e}")
                impressum_result = None

        # If the first URL fails or returns bot detection, try common patterns
        if not impressum_result or not impressum_result.success or _is_bot_detection_content(impressum_result.html):
            logger.warning(
                f"Primary impressum URL failed or blocked: {impressum_url}")

            # Try common impressum URL patterns
            common_patterns = [
                "/hilfe/impressum",
                "/impressum",
                "/imprint",
                "/legal",
                "/legal-notice",
                "/rechtliches"
            ]

            for pattern in common_patterns:
                test_url = urljoin(base_url, pattern)
                if test_url == impressum_url:
                    continue  # Skip the one we already tried

                logger.info(f"Trying alternative impressum URL: {test_url}")
                try:
                    test_result = await crawler.arun(url=test_url, config=crawler_config)

                    if test_result.success and not _is_bot_detection_content(test_result.html):
                        logger.info(
                            f"Successfully found impressum at: {test_url}")
                        impressum_result = test_result
                        impressum_url = test_url
                        break
                except Exception as e:
                    logger.warning(
                        f"Error trying alternative URL {test_url}: {e}")
                    continue
            else:
                logger.error(
                    f"All impressum URL attempts failed for {base_url}")
                return None, None

        if not impressum_result.success:
            logger.error(
                f"Failed to crawl Impressum page {impressum_url}: {impressum_result.error_message}")
            return None, None

        # 4. Save the Impressum's HTML and return the content.
        saved_html_path = save_html_to_dir(
            impressum_result.html, impressum_url)

        # Apply additional content filtering for better LLM extraction
        raw_content = impressum_result.markdown
        logger.info(
            f"Raw impressum content length: {len(raw_content)} characters")

        # Apply LLM-based content filtering for focused extraction
        try:
            impressum_filter = _create_impressum_content_filter()
            filtered_content = impressum_filter.filter_content(raw_content)

            if filtered_content and len(filtered_content) > 0:
                # Join filtered content if it's a list
                if isinstance(filtered_content, list):
                    final_content = "\n".join(filtered_content)
                else:
                    final_content = filtered_content

                logger.info(
                    f"Filtered impressum content length: {len(final_content)} characters")
                return final_content, saved_html_path
            else:
                logger.warning(
                    "Content filtering returned empty result, using raw markdown")
                return raw_content, saved_html_path

        except Exception as e:
            logger.warning(
                f"Content filtering failed: {e}, using raw markdown")
            return raw_content, saved_html_path


def _create_impressum_content_filter():
    """
    Creates a specialized LLM content filter for impressum pages.

    Returns:
        LLMContentFilter: Configured filter for impressum content extraction
    """
    return LLMContentFilter(
        llm_config=LLMConfig(
            provider="ollama/qwen3",  # Use a fast, efficient model for filtering
        ),
        instruction="""
        Extract ONLY the core company/legal information from this impressum/imprint page.

        INCLUDE these sections and German terms:
        - Company name and legal structure (GmbH, AG, e.K., KG, OHG, UG, GbR)
        - Managing director/owner: "Geschäftsführer", "Inhaber", "Vertreten durch", "Vertretungsberechtigter"
        - Address: "Anschrift", "Adresse", "Sitz", street, city, postal code
        - Contact: phone, email, website
        - Legal registration: "Handelsregister", "Registergericht", "Registernummer", "HRB", "HRA"
        - Tax information: "Steuernummer", "Steuer-Nr.", "USt-IdNr.", "Umsatzsteuer-ID", "USt-ID"

        EXCLUDE completely:
        - Navigation menus and links
        - Header and footer content
        - Cookie notices and privacy banners
        - Social media links and buttons
        - General website content not related to company info
        - Advertising and promotional content
        - Terms of service and privacy policy details (unless they contain company info)
        - JavaScript code and technical elements

        Return only the essential company information in clean, structured format.
        Keep the original German terms and formatting where relevant.
        """,
        chunk_token_threshold=2048,  # Smaller chunks for focused extraction
        verbose=False,
    )


def _create_impressum_markdown_generator():
    """
    Creates a specialized markdown generator optimized for impressum content.

    Returns:
        DefaultMarkdownGenerator: Configured for impressum extraction
    """
    return DefaultMarkdownGenerator(
        content_source="fit_html",  # Use preprocessed HTML optimized for extraction
        options={
            "ignore_links": True,  # Remove navigation and external links
            "escape_html": False,  # Keep HTML entities readable
            "body_width": 120,  # Reasonable line width
            "include_meta": False,  # Skip meta information
            "include_raw_html": False,  # Clean markdown only
        }
    )


def _is_bot_detection_content(html_content: str) -> bool:
    """
    Check if the HTML content indicates bot detection or blocking.

    Args:
        html_content: HTML content to check

    Returns:
        True if bot detection is detected, False otherwise
    """
    if not html_content:
        return True

    # If content is very short, it's likely a bot detection page
    if len(html_content.strip()) < 1000:
        content_lower = html_content.lower()
        bot_indicators = [
            "pardon our interruption",
            "browser made us think you were a bot",
            "cloudflare",
            "access denied",
            "blocked",
            "captcha",
            "security check",
            "please verify you are human",
            "bot detection",
            "checking your browser",
            "enable javascript",
            "javascript is disabled"
        ]

        # If it's short content and contains bot indicators, it's likely bot detection
        if any(indicator in content_lower for indicator in bot_indicators):
            return True

    # Check for typical impressum content to determine if it's valid
    content_lower = html_content.lower()
    impressum_indicators = [
        "impressum",
        "imprint",
        "legal notice",
        "rechtliche hinweise",
        "geschäftsführer",
        "managing director",
        "handelsregister",
        "commercial register",
        "umsatzsteuer",
        "vat",
        "telefon",
        "phone",
        "e-mail",
        "address",
        "adresse"
    ]

    # If it contains impressum-related content, it's probably valid
    if any(indicator in content_lower for indicator in impressum_indicators):
        return False

    # If content is very short and doesn't contain impressum indicators, likely bot detection
    return len(html_content.strip()) < 500


@track_crawl(crawl_type=CrawlType.IMPRESSUM, extract_url_from_args=False)
async def crawl_multiple_impressums(urls: Union[str, List[str]]) -> List[Dict[str, any]]:
    """
    Crawl impressum pages from multiple websites.

    Args:
        urls: Single URL string or list of URLs to crawl impressums from

    Returns:
        List of dictionaries containing impressum crawl results for each URL
    """
    # Normalize input to list
    if isinstance(urls, str):
        url_list = [urls]
    else:
        url_list = urls

    results = []

    for i, url in enumerate(url_list, 1):
        logger.info(f"Crawling impressum {i}/{len(url_list)}: {url}")

        try:
            # Crawl individual impressum
            markdown_content, html_path = await crawl_impressum(url)

            if markdown_content:
                impressum_result = {
                    "url": url,
                    "success": True,
                    "markdown_content": markdown_content,
                    "html_path": html_path,
                    "error": None
                }
                logger.info(f"Successfully crawled impressum from {url}")
            else:
                impressum_result = {
                    "url": url,
                    "success": False,
                    "markdown_content": None,
                    "html_path": None,
                    "error": "No impressum content found"
                }
                logger.warning(f"No impressum content found for {url}")

            results.append(impressum_result)

        except Exception as e:
            logger.error(f"Failed to crawl impressum from {url}: {e}")
            error_result = {
                "url": url,
                "success": False,
                "markdown_content": None,
                "html_path": None,
                "error": str(e)
            }
            results.append(error_result)

    logger.info(
        f"Completed crawling impressums from {len(url_list)} URLs. Success: {sum(1 for r in results if r['success'])}/{len(url_list)}")
    return results


@track_crawl(crawl_type=CrawlType.IMPRESSUM, extract_url_from_args=False)
async def extract_multiple_company_data(
    urls: Union[str, List[str]],
    extractor_type: ExtractorType = ExtractorType.AUTO
) -> List[CompanyData]:
    """
    Extract structured company data from multiple websites.

    Args:
        urls: Single URL string or list of URLs to extract company data from
        extractor_type: Type of extractor to use (AUTO, REGEX, or LLM)

    Returns:
        List of CompanyData instances with extracted company information
    """
    # Normalize input to list
    if isinstance(urls, str):
        url_list = [urls]
    else:
        url_list = urls

    results = []

    for i, url in enumerate(url_list, 1):
        logger.info(f"Extracting company data {i}/{len(url_list)}: {url}")

        try:
            company_data = await extract_company_data(url, extractor_type)
            results.append(company_data)

            if company_data.is_valid_extraction():
                logger.info(
                    f"Successfully extracted company data from {url}: {company_data.get_summary()}")
            else:
                logger.warning(
                    f"Low-quality company data extracted from {url}")

        except Exception as e:
            logger.error(f"Failed to extract company data from {url}: {e}")
            error_data = CompanyData(
                source_url=url,
                extraction_method="error",
                confidence_score=0.0,
                additional_info={"error": str(e)}
            )
            results.append(error_data)

    successful_extractions = sum(
        1 for data in results if data.is_valid_extraction())
    logger.info(
        f"Completed company data extraction from {len(url_list)} URLs. "
        f"Successful: {successful_extractions}/{len(url_list)}"
    )

    return results
