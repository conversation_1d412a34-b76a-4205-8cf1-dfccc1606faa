"""
Sitemap crawling functionality for extracting URLs from XML sitemaps.

This module contains functions for:
- Reading sitemap.xml files from standard locations
- Parsing robots.txt to find sitemap locations
- Handling both regular sitemaps and sitemap indexes
- Recursively processing nested sitemaps
- Supporting compressed (.xml.gz) sitemaps
- Extracting and deduplicating URLs
"""

import gzip
import xml.etree.ElementTree as ET
from typing import Dict, List, Set, Optional, Union
from urllib.parse import urljoin, urlparse
import aiohttp
import asyncio

from utils.logger import logger
from .statistics import track_crawl, CrawlType


@track_crawl(crawl_type=CrawlType.SITEMAP)
async def read_sitemap(url: str, timeout: int = 30) -> Dict[str, Union[str, List[str]]]:
    """
    Read and parse sitemap(s) from a website, extracting all crawlable URLs.

    This function:
    1. Tries the standard sitemap location (https://domain/sitemap.xml)
    2. Falls back to robots.txt to find sitemap locations
    3. Handles both regular sitemaps and sitemap indexes
    4. Recursively processes nested sitemaps
    5. Supports both .xml and .xml.gz formats

    Args:
        url (str): The base URL of the website (e.g., "https://example.com")
        timeout (int): Request timeout in seconds (default: 30)

    Returns:
        Dict containing:
        - "source": The primary sitemap URL that was found
        - "urls": List of all unique URLs extracted from sitemap(s)

    Example:
        {
            "source": "https://example.com/sitemap.xml",
            "urls": ["https://example.com/page1", "https://example.com/page2", ...]
        }
    """
    # Normalize the base URL
    parsed_url = urlparse(url)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

    logger.info(f"Starting sitemap extraction for: {base_url}")

    # Try standard sitemap location first
    standard_sitemap_url = urljoin(base_url, "/sitemap.xml")

    try:
        urls = await _extract_urls_from_sitemap(standard_sitemap_url, timeout)
        if urls:
            logger.info(
                f"Found sitemap at standard location: {standard_sitemap_url}")
            return {
                "source": standard_sitemap_url,
                "urls": list(urls)
            }
    except Exception as e:
        logger.debug(f"Standard sitemap location failed: {e}")

    # Fallback to robots.txt
    logger.info("Standard sitemap not found, checking robots.txt")
    sitemap_urls = await _find_sitemaps_in_robots(base_url, timeout)

    if not sitemap_urls:
        logger.warning(f"No sitemaps found for {base_url}")
        return {
            "source": None,
            "urls": []
        }

    # Process all sitemaps found in robots.txt
    all_urls = set()
    primary_source = sitemap_urls[0]  # Use first sitemap as primary source

    for sitemap_url in sitemap_urls:
        try:
            urls = await _extract_urls_from_sitemap(sitemap_url, timeout)
            all_urls.update(urls)
            logger.info(f"Extracted {len(urls)} URLs from {sitemap_url}")
        except Exception as e:
            logger.error(f"Failed to process sitemap {sitemap_url}: {e}")
            continue

    logger.info(f"Total unique URLs extracted: {len(all_urls)}")
    return {
        "source": primary_source,
        "urls": list(all_urls)
    }


async def _find_sitemaps_in_robots(base_url: str, timeout: int) -> List[str]:
    """
    Parse robots.txt to find sitemap URLs.

    Args:
        base_url (str): The base URL of the website
        timeout (int): Request timeout in seconds

    Returns:
        List[str]: List of sitemap URLs found in robots.txt
    """
    robots_url = urljoin(base_url, "/robots.txt")
    sitemap_urls = []

    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.get(robots_url) as response:
                if response.status == 200:
                    content = await response.text()

                    # Parse robots.txt for sitemap entries
                    for line in content.split('\n'):
                        line = line.strip()
                        if line.lower().startswith('sitemap:'):
                            sitemap_url = line.split(':', 1)[1].strip()
                            if sitemap_url:
                                sitemap_urls.append(sitemap_url)
                                logger.debug(
                                    f"Found sitemap in robots.txt: {sitemap_url}")

                else:
                    logger.debug(
                        f"robots.txt not accessible: HTTP {response.status}")

    except Exception as e:
        logger.debug(f"Error reading robots.txt from {robots_url}: {e}")

    return sitemap_urls


async def _extract_urls_from_sitemap(sitemap_url: str, timeout: int) -> Set[str]:
    """
    Extract URLs from a single sitemap, handling both regular sitemaps and sitemap indexes.

    Args:
        sitemap_url (str): URL of the sitemap to process
        timeout (int): Request timeout in seconds

    Returns:
        Set[str]: Set of URLs extracted from the sitemap
    """
    urls = set()

    try:
        # Download sitemap content
        content = await _download_sitemap_content(sitemap_url, timeout)
        if not content:
            return urls

        # Parse XML content
        try:
            root = ET.fromstring(content)
        except ET.ParseError as e:
            logger.error(f"Invalid XML in sitemap {sitemap_url}: {e}")
            return urls

        # Handle sitemap index (contains references to other sitemaps)
        if _is_sitemap_index(root):
            logger.debug(f"Processing sitemap index: {sitemap_url}")
            nested_sitemap_urls = _extract_sitemap_urls_from_index(root)

            # Recursively process each nested sitemap
            for nested_url in nested_sitemap_urls:
                try:
                    nested_urls = await _extract_urls_from_sitemap(nested_url, timeout)
                    urls.update(nested_urls)
                    logger.debug(
                        f"Extracted {len(nested_urls)} URLs from nested sitemap: {nested_url}")
                except Exception as e:
                    logger.error(
                        f"Failed to process nested sitemap {nested_url}: {e}")
                    continue

        # Handle regular sitemap (contains actual page URLs)
        else:
            logger.debug(f"Processing regular sitemap: {sitemap_url}")
            page_urls = _extract_page_urls_from_sitemap(root)
            urls.update(page_urls)
            logger.debug(
                f"Extracted {len(page_urls)} URLs from sitemap: {sitemap_url}")

    except Exception as e:
        logger.error(f"Error processing sitemap {sitemap_url}: {e}")
        raise

    return urls


async def _download_sitemap_content(sitemap_url: str, timeout: int) -> Optional[str]:
    """
    Download and decompress sitemap content, handling both .xml and .xml.gz formats.

    Args:
        sitemap_url (str): URL of the sitemap to download
        timeout (int): Request timeout in seconds

    Returns:
        Optional[str]: The sitemap content as string, or None if download failed
    """
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.get(sitemap_url) as response:
                if response.status != 200:
                    logger.error(
                        f"Failed to download sitemap {sitemap_url}: HTTP {response.status}")
                    return None

                content_bytes = await response.read()

                # Handle gzipped content
                if sitemap_url.endswith('.gz') or response.headers.get('content-encoding') == 'gzip':
                    try:
                        content_bytes = gzip.decompress(content_bytes)
                        logger.debug(
                            f"Decompressed gzipped sitemap: {sitemap_url}")
                    except gzip.BadGzipFile:
                        # Not actually gzipped, continue with original content
                        logger.debug(
                            f"Content not gzipped despite headers/URL: {sitemap_url}")
                    except Exception as e:
                        logger.error(
                            f"Failed to decompress gzipped sitemap {sitemap_url}: {e}")
                        return None

                # Decode to string
                try:
                    content = content_bytes.decode('utf-8')
                    return content
                except UnicodeDecodeError:
                    # Try with different encodings
                    for encoding in ['latin-1', 'cp1252']:
                        try:
                            content = content_bytes.decode(encoding)
                            logger.debug(
                                f"Decoded sitemap with {encoding} encoding: {sitemap_url}")
                            return content
                        except UnicodeDecodeError:
                            continue

                    logger.error(
                        f"Failed to decode sitemap content: {sitemap_url}")
                    return None

    except asyncio.TimeoutError:
        logger.error(f"Timeout downloading sitemap: {sitemap_url}")
        return None
    except Exception as e:
        logger.error(f"Error downloading sitemap {sitemap_url}: {e}")
        return None


def _is_sitemap_index(root: ET.Element) -> bool:
    """
    Determine if the XML root represents a sitemap index or a regular sitemap.

    Args:
        root (ET.Element): The root element of the parsed XML

    Returns:
        bool: True if it's a sitemap index, False if it's a regular sitemap
    """
    # Check for sitemapindex tag or sitemap children
    if root.tag.endswith('sitemapindex'):
        return True

    # Check if it contains sitemap elements (not url elements)
    for child in root:
        if child.tag.endswith('sitemap'):
            return True

    return False


def _extract_sitemap_urls_from_index(root: ET.Element) -> List[str]:
    """
    Extract sitemap URLs from a sitemap index.

    Args:
        root (ET.Element): The root element of the sitemap index XML

    Returns:
        List[str]: List of sitemap URLs found in the index
    """
    sitemap_urls = []

    # Handle different XML namespaces
    for sitemap in root.iter():
        if sitemap.tag.endswith('sitemap'):
            for loc in sitemap.iter():
                if loc.tag.endswith('loc') and loc.text:
                    sitemap_url = loc.text.strip()
                    if sitemap_url:
                        sitemap_urls.append(sitemap_url)
                        break  # Only take the first loc element per sitemap

    return sitemap_urls


def _extract_page_urls_from_sitemap(root: ET.Element) -> List[str]:
    """
    Extract page URLs from a regular sitemap.

    Args:
        root (ET.Element): The root element of the sitemap XML

    Returns:
        List[str]: List of page URLs found in the sitemap
    """
    page_urls = []

    # Handle different XML namespaces
    for url_element in root.iter():
        if url_element.tag.endswith('url'):
            for loc in url_element.iter():
                if loc.tag.endswith('loc') and loc.text:
                    page_url = loc.text.strip()
                    if page_url:
                        page_urls.append(page_url)
                        break  # Only take the first loc element per url

    return page_urls


@track_crawl(crawl_type=CrawlType.SITEMAP, extract_url_from_args=False)
async def read_multiple_sitemaps(urls: List[str], timeout: int = 30) -> List[Dict[str, Union[str, List[str]]]]:
    """
    Read and parse sitemaps from multiple websites concurrently.

    Args:
        urls (List[str]): List of base URLs to extract sitemaps from
        timeout (int): Request timeout in seconds (default: 30)

    Returns:
        List[Dict]: List of sitemap results, each containing:
        - "base_url": The original base URL
        - "source": The sitemap URL that was found (or None)
        - "urls": List of URLs extracted from sitemap(s)
        - "success": Boolean indicating if extraction was successful
        - "error": Error message if extraction failed
    """
    logger.info(f"Starting batch sitemap extraction for {len(urls)} websites")

    async def process_single_sitemap(base_url: str) -> Dict[str, Union[str, List[str], bool]]:
        """Process a single sitemap and return structured result."""
        try:
            result = await read_sitemap(base_url, timeout)
            return {
                "base_url": base_url,
                "source": result["source"],
                "urls": result["urls"],
                "success": len(result["urls"]) > 0,
                "error": None
            }
        except Exception as e:
            logger.error(f"Failed to process sitemap for {base_url}: {e}")
            return {
                "base_url": base_url,
                "source": None,
                "urls": [],
                "success": False,
                "error": str(e)
            }

    # Process all sitemaps concurrently
    tasks = [process_single_sitemap(url) for url in urls]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Handle any exceptions that occurred during processing
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Exception processing {urls[i]}: {result}")
            processed_results.append({
                "base_url": urls[i],
                "source": None,
                "urls": [],
                "success": False,
                "error": str(result)
            })
        else:
            processed_results.append(result)

    successful_count = sum(1 for r in processed_results if r["success"])
    logger.info(
        f"Batch sitemap extraction completed: {successful_count}/{len(urls)} successful")

    return processed_results
