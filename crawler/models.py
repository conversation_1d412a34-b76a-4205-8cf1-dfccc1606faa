"""
Pydantic models for structured data extraction from web crawling.

This module defines strict data schemas for consistent JSON output
from various crawling and extraction operations.
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from enum import Enum
import re


class LegalStructure(str, Enum):
    """Enumeration of legal business structures."""
    # German legal structures
    GMBH = "GmbH"
    AG = "AG"
    EK = "e.K."
    KG = "KG"
    OHG = "OHG"
    UG = "UG"
    GBR = "GbR"

    # International legal structures
    LLC = "LLC"
    CORP = "Corp"
    INC = "Inc"
    LTD = "Ltd"
    PLC = "PLC"
    SA = "SA"
    SRL = "SRL"
    BV = "B.V."
    AB = "AB"

    # Other/Unknown
    OTHER = "Other"
    UNKNOWN = "Unknown"


class Country(str, Enum):
    """Enumeration of countries."""
    GERMANY = "Germany"
    AUSTRIA = "Austria"
    SWITZERLAND = "Switzerland"
    NETHERLANDS = "Netherlands"
    BELGIUM = "Belgium"
    FRANCE = "France"
    ITALY = "Italy"
    SPAIN = "Spain"
    UK = "United Kingdom"
    USA = "United States"
    CANADA = "Canada"
    OTHER = "Other"
    UNKNOWN = "Unknown"


class CompanyData(BaseModel):
    """
    Strict schema for company data extracted from legal pages (Impressum/Imprint).

    This model ensures consistent JSON structure with never-varying key names
    and hierarchy. All fields are always present in the output.
    """

    # Core company information
    company_name: str = Field(
        default="",
        description="Full legal company name",
        max_length=200
    )

    legal_structure: LegalStructure = Field(
        default=LegalStructure.UNKNOWN,
        description="Legal structure/entity type (GmbH, AG, LLC, etc.)"
    )

    # Address information
    street_address: str = Field(
        default="",
        description="Street address including house number",
        max_length=200
    )

    zip_code: str = Field(
        default="",
        description="Postal/ZIP code",
        max_length=20
    )

    city: str = Field(
        default="",
        description="City name",
        max_length=100
    )

    state_province: str = Field(
        default="",
        description="State, province, or region",
        max_length=100
    )

    country: Country = Field(
        default=Country.UNKNOWN,
        description="Country"
    )

    # Contact information
    phone: str = Field(
        default="",
        description="Primary phone number",
        max_length=50
    )

    fax: str = Field(
        default="",
        description="Fax number",
        max_length=50
    )

    email: str = Field(
        default="",
        description="Primary email address",
        max_length=100
    )

    website: str = Field(
        default="",
        description="Company website URL",
        max_length=200
    )

    # Management information
    managing_director: str = Field(
        default="",
        description="Managing director, CEO, or owner name",
        max_length=200
    )

    management_title: str = Field(
        default="",
        description="Title of the managing person (Geschäftsführer, CEO, etc.)",
        max_length=100
    )

    # Legal registration information
    registration_number: str = Field(
        default="",
        description="Commercial register number (HRB, HRA, etc.)",
        max_length=50
    )

    register_court: str = Field(
        default="",
        description="Registration court (Registergericht)",
        max_length=100
    )

    # Tax information
    tax_id: str = Field(
        default="",
        description="Tax identification number (Steuernummer)",
        max_length=50
    )

    vat_id: str = Field(
        default="",
        description="VAT identification number (USt-IdNr.)",
        max_length=50
    )

    # Additional information
    additional_info: Dict[str, str] = Field(
        default_factory=dict,
        description="Additional extracted information as key-value pairs"
    )

    # Extraction metadata
    extraction_method: str = Field(
        default="unknown",
        description="Method used for extraction (regex, llm, manual)"
    )

    confidence_score: float = Field(
        default=0.0,
        description="Confidence score of extraction (0.0-1.0)",
        ge=0.0,
        le=1.0
    )

    source_url: str = Field(
        default="",
        description="URL where the data was extracted from",
        max_length=500
    )

    extraction_timestamp: str = Field(
        default="",
        description="ISO timestamp of when extraction was performed",
        max_length=50
    )

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Validate email format if provided."""
        if v and v.strip():
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, v.strip()):
                return ""  # Return empty string for invalid emails
        return v.strip() if v else ""

    @field_validator('website')
    @classmethod
    def validate_website(cls, v):
        """Validate website URL format if provided."""
        if v and v.strip():
            cleaned = v.strip()

            # Basic URL validation - must contain a dot and valid characters
            if '.' not in cleaned or ' ' in cleaned:
                return ""

            # Add protocol if missing
            if not cleaned.startswith(('http://', 'https://')):
                cleaned = 'https://' + cleaned

            # More strict URL pattern
            url_pattern = r'^https?://[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*(/.*)?$'
            if re.match(url_pattern, cleaned):
                return cleaned
            else:
                return ""  # Return empty string for invalid URLs
        return v.strip() if v else ""

    @field_validator('phone', 'fax')
    @classmethod
    def validate_phone_numbers(cls, v):
        """Clean and validate phone/fax numbers."""
        if v and v.strip():
            # Remove common formatting and keep only digits, +, -, (, ), and spaces
            cleaned = re.sub(r'[^\d+\-\(\)\s]', '', v.strip())
            return cleaned
        return ""

    @model_validator(mode='after')
    def validate_required_data(self):
        """Ensure at least some meaningful data is present."""
        required_fields = ['company_name',
                           'street_address', 'city', 'email', 'phone']
        has_data = any(getattr(self, field, "").strip()
                       for field in required_fields)

        if not has_data and self.confidence_score > 0.0:
            # Only set confidence to 0 if it's not already 0 to avoid recursion
            object.__setattr__(self, 'confidence_score', 0.0)

        return self

    def is_valid_extraction(self) -> bool:
        """Check if the extraction contains meaningful data."""
        return self.confidence_score > 0.0 and (
            bool(self.company_name.strip()) or
            bool(self.street_address.strip()) or
            bool(self.email.strip()) or
            bool(self.phone.strip())
        )

    def get_summary(self) -> str:
        """Get a human-readable summary of the company data."""
        parts = []

        if self.company_name:
            name_part = self.company_name
            if self.legal_structure != LegalStructure.UNKNOWN:
                # Handle both enum and string values
                structure_value = self.legal_structure if isinstance(
                    self.legal_structure, str) else self.legal_structure.value
                name_part += f" ({structure_value})"
            parts.append(name_part)

        if self.street_address or self.city:
            address_parts = [p for p in [
                self.street_address, self.zip_code, self.city] if p]
            if address_parts:
                parts.append(", ".join(address_parts))

        if self.email:
            parts.append(f"Email: {self.email}")

        if self.phone:
            parts.append(f"Phone: {self.phone}")

        return " | ".join(parts) if parts else "No company data available"

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
        extra="forbid"  # Prevent additional fields
    )
