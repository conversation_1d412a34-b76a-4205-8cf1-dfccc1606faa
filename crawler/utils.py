"""
Crawler-specific utility functions.

This module contains utility functions specifically for crawler operations:
- Link extraction and categorization
- Page title extraction
- Link file saving functionality
"""

import os
import json
from datetime import datetime
from typing import Dict, List
from urllib.parse import urlparse

from utils.logger import logger


def _extract_page_title(result) -> str:
    """
    Extract page title from crawl4ai result.

    Args:
        result: The crawl4ai result object

    Returns:
        str: The page title or a fallback value
    """
    # Try to get title from various sources
    if hasattr(result, 'title') and result.title:
        return result.title.strip() if result.title else "Unknown Page"

    if hasattr(result, 'metadata') and result.metadata:
        if isinstance(result.metadata, dict) and 'title' in result.metadata:
            title = result.metadata['title']
            return title.strip() if title else "Unknown Page"

    # Try to extract title from HTML
    if hasattr(result, 'html') and result.html:
        import re
        title_match = re.search(
            r'<title[^>]*>(.*?)</title>', result.html, re.IGNORECASE | re.DOTALL)
        if title_match:
            title = title_match.group(1).strip()
            # Clean up HTML entities and extra whitespace
            title = re.sub(r'\s+', ' ', title)
            return title

    # Try to extract from markdown content (first line if it's a header)
    if hasattr(result, 'markdown') and result.markdown:
        markdown_content = result.markdown.strip() if result.markdown else ""
        if markdown_content:
            lines = markdown_content.split('\n')
            if lines and lines[0].startswith('#'):
                return lines[0].lstrip('#').strip()

    return "Unknown Page"


def _extract_and_categorize_links(base_url: str, links: Dict) -> Dict[str, List[Dict]]:
    """
    Extract and categorize links from crawl4ai result.links into internal and external links.

    Args:
        base_url (str): The base URL of the crawled page
        links (Dict): Dictionary with 'internal' and 'external' keys from crawl4ai result.links

    Returns:
        Dict with 'internal' and 'external' keys containing lists of link objects with URL and title
    """
    """
    Process the links dictionary from Crawl4AI and enhance with title information.
    Crawl4AI already categorizes links as internal/external, so we just need to format them.
    """

    def process_link_list(link_list):
        """Process a list of link objects and return enhanced link data."""
        processed_links = []

        for link_obj in link_list:
            if isinstance(link_obj, dict):
                # Extract link information
                url = link_obj.get('href', '')
                text = (link_obj.get('text') or '').strip()
                title = (link_obj.get('title') or '').strip()

                # Use text as title if title is empty, or use URL as fallback
                display_title = title or text or url

                # Create enhanced link object
                enhanced_link = {
                    'url': url,
                    'title': display_title,
                    'text': text,
                    'html_title': title  # Original HTML title attribute
                }

                processed_links.append(enhanced_link)
            elif isinstance(link_obj, str):
                # Handle case where link is just a string URL
                processed_links.append({
                    'url': link_obj,
                    'title': link_obj,
                    'text': '',
                    'html_title': ''
                })

        return processed_links

    # Process internal and external links
    result = {
        'internal': process_link_list(links.get('internal', [])),
        'external': process_link_list(links.get('external', []))
    }

    total_internal = len(result['internal'])
    total_external = len(result['external'])

    logger.info(
        f"Link extraction summary: {total_internal} internal, {total_external} external")

    return result


def save_links_to_file(links_data: Dict[str, List[Dict]], base_url: str, page_title: str = None) -> str:
    """
    Save extracted links to a JSON file.

    Args:
        links_data (Dict): Dictionary with 'internal' and 'external' link lists
        base_url (str): The base URL that was crawled
        page_title (str, optional): The title of the crawled page

    Returns:
        str: Path to the saved links file
    """
    # Extract domain from URL (same logic as in file_utils.py)
    domain = urlparse(base_url).netloc.replace("www.", "")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create links directory if it doesn't exist
    links_dir = os.path.join("results", domain, "links")
    os.makedirs(links_dir, exist_ok=True)

    # Save links data
    links_file = os.path.join(links_dir, f"links_{timestamp}.json")

    links_summary = {
        'base_url': base_url,
        'page_title': page_title or 'Unknown',
        'crawled_at': datetime.now().isoformat(),
        'summary': {
            'total_internal': len(links_data['internal']),
            'total_external': len(links_data['external']),
            'total_links': len(links_data['internal']) + len(links_data['external'])
        },
        'links': links_data
    }

    with open(links_file, 'w', encoding='utf-8') as f:
        json.dump(links_summary, f, indent=2, ensure_ascii=False)

    logger.info(f"Links saved to: {links_file}")
    return links_file
