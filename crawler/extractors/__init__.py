"""
Company data extractors for structured information extraction.

This package provides different extraction strategies for company data
from legal pages (Impressum/Imprint) with a unified interface.
"""

from .base import CompanyDataExtractor, ExtractionResult
from .regex_extractor import RegexCompanyDataExtractor
from .llm_extractor import LLMCompanyDataExtractor
from .factory import ExtractorFactory, ExtractorType, get_factory

__all__ = [
    "CompanyDataExtractor",
    "ExtractionResult",
    "RegexCompanyDataExtractor",
    "LLMCompanyDataExtractor",
    "ExtractorFactory",
    "ExtractorType",
    "get_factory"
]
