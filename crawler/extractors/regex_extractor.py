"""
Regex-based company data extractor.

This module provides fast, rule-based extraction of company data using
regular expressions and pattern matching. Optimized for German legal
pages (Impressum) but supports international formats.
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from bs4 import BeautifulSoup
import html

from .base import CompanyDataExtractor, ExtractionResult
from crawler.models import LegalStructure, Country
from utils.logger import logger


class RegexCompanyDataExtractor(CompanyDataExtractor):
    """
    Fast regex-based extractor for company data from legal pages.

    Uses pattern matching and rule-based logic to extract structured
    company information without requiring LLM processing.
    """

    def __init__(self):
        """Initialize the regex extractor with compiled patterns."""
        super().__init__("regex")
        self._compile_patterns()

    def _compile_patterns(self):
        """Compile all regex patterns for efficient matching."""

        # Company name patterns (German focus)
        self.company_patterns = [
            # Direct company name patterns
            re.compile(
                r'(?:Firma|Unternehmen|Company):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(
                r'([A-ZÄÖÜ][a-zäöüß\s&-]+(?:GmbH|AG|e\.K\.|KG|OHG|UG|GbR|LLC|Inc|Ltd|Corp))', re.MULTILINE),
            # Name before legal structure
            re.compile(
                r'([A-ZÄÖÜ][a-zäöüß\s&-]{2,50})\s+(GmbH|AG|e\.K\.|KG|OHG|UG|GbR)', re.MULTILINE),
        ]

        # Legal structure patterns
        self.legal_structure_patterns = [
            re.compile(
                r'\b(GmbH|AG|e\.K\.|KG|OHG|UG|GbR|LLC|Inc|Ltd|Corp|PLC|SA|SRL|B\.V\.|AB)\b', re.IGNORECASE),
        ]

        # Address patterns
        self.address_patterns = [
            # German address format
            re.compile(
                r'(?:Anschrift|Adresse|Address):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(
                r'([A-ZÄÖÜ][a-zäöüß\s-]+(?:straße|str\.|gasse|weg|platz|allee))\s*(\d+[a-z]?)', re.IGNORECASE),
            # International formats
            re.compile(
                r'(\d+)\s+([A-Z][a-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr))', re.IGNORECASE),
        ]

        # ZIP code and city patterns
        self.zip_city_patterns = [
            # German format: 12345 City
            re.compile(r'\b(\d{5})\s+([A-ZÄÖÜ][a-zäöüß\s-]+)\b'),
            # International formats
            re.compile(
                # UK
                r'\b([A-Z]{1,2}\d{1,2}[A-Z]?\s?\d[A-Z]{2})\s+([A-Z][a-z\s-]+)\b'),
            re.compile(r'\b(\d{5}(?:-\d{4})?)\s+([A-Z][a-z\s-]+)\b'),  # US ZIP
        ]

        # Phone patterns
        self.phone_patterns = [
            re.compile(
                r'(?:Tel|Telefon|Phone):\s*([+\d\s\-\(\)\/]+)', re.IGNORECASE),
            re.compile(
                r'\+49\s*[\d\s\-\(\)\/]+|\b0\d{2,4}[\s\-\/]\d+', re.IGNORECASE),
            re.compile(
                r'(?:Tel|Phone)\.?\s*:?\s*([+\d\s\-\(\)\/]{8,})', re.IGNORECASE),
        ]

        # Email patterns
        self.email_patterns = [
            re.compile(
                r'(?:E-?Mail|Email):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', re.IGNORECASE),
            re.compile(
                r'\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b'),
        ]

        # Website patterns
        self.website_patterns = [
            re.compile(
                r'(?:Website|Web|Internet):\s*(https?://[^\s]+)', re.IGNORECASE),
            re.compile(r'\b(https?://[^\s]+)\b'),
            re.compile(
                r'(?:www\.)([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', re.IGNORECASE),
        ]

        # Managing director patterns (German focus)
        self.director_patterns = [
            re.compile(
                r'(?:Geschäftsführer|Managing Director|CEO|Inhaber|Vertreten durch):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(
                r'(?:Geschäftsführer|Inhaber):\s*([A-ZÄÖÜ][a-zäöüß\s-]+)', re.IGNORECASE),
        ]

        # Registration patterns
        self.registration_patterns = [
            re.compile(
                r'(?:Handelsregister|Commercial Register):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(r'(?:HRB|HRA)\s*(\d+)', re.IGNORECASE),
            re.compile(
                r'(?:Registernummer|Registration Number):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
        ]

        # Court patterns
        self.court_patterns = [
            re.compile(
                r'(?:Registergericht|Register Court):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(
                r'(?:Amtsgericht|AG)\s+([A-ZÄÖÜ][a-zäöüß\s-]+)', re.IGNORECASE),
        ]

        # Tax ID patterns
        self.tax_patterns = [
            re.compile(
                r'(?:Steuernummer|Steuer-Nr\.|Tax Number):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
            re.compile(
                r'(?:USt-IdNr\.|Umsatzsteuer-ID|USt-ID|VAT ID):\s*([^\n\r]+)', re.IGNORECASE | re.MULTILINE),
        ]

    async def extract(self, html_content: str, source_url: str = "") -> ExtractionResult:
        """
        Extract company data using regex patterns.

        Args:
            html_content: Raw HTML content to extract from
            source_url: Source URL for metadata

        Returns:
            ExtractionResult with extracted company data
        """
        start_time = self._start_timing()

        try:
            # Clean and prepare content
            text_content = self._prepare_content(html_content)

            if not text_content.strip():
                return self._create_result(
                    data={},
                    success=False,
                    confidence_score=0.0,
                    processing_time_ms=self._end_timing(start_time),
                    source_url=source_url,
                    error_message="No text content found",
                    raw_content_length=len(html_content),
                    processed_content_length=0
                )

            # Extract data using patterns
            extracted_data = self._extract_all_fields(text_content)

            # Calculate confidence score
            confidence = self._calculate_confidence(extracted_data)

            # Determine success
            success = confidence > 0.1  # Minimum threshold for success

            return self._create_result(
                data=extracted_data,
                success=success,
                confidence_score=confidence,
                processing_time_ms=self._end_timing(start_time),
                source_url=source_url,
                raw_content_length=len(html_content),
                processed_content_length=len(text_content)
            )

        except Exception as e:
            logger.error(f"Regex extraction failed: {e}")
            return self._create_result(
                data={},
                success=False,
                confidence_score=0.0,
                processing_time_ms=self._end_timing(start_time),
                source_url=source_url,
                error_message=str(e),
                raw_content_length=len(html_content)
            )

    def _prepare_content(self, html_content: str) -> str:
        """
        Clean and prepare HTML content for regex extraction.

        Args:
            html_content: Raw HTML content

        Returns:
            Cleaned text content
        """
        try:
            # Parse HTML and extract text
            soup = BeautifulSoup(html_content, 'html.parser')

            # Remove script and style elements
            for script in soup(["script", "style", "nav", "header", "footer"]):
                script.decompose()

            # Get text content
            text = soup.get_text()

            # Clean up whitespace and decode HTML entities
            text = html.unescape(text)
            text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
            text = re.sub(r'\n\s*\n', '\n', text)  # Remove empty lines

            return text.strip()

        except Exception as e:
            logger.warning(f"HTML parsing failed, using raw content: {e}")
            # Fallback to raw content cleaning
            text = re.sub(r'<[^>]+>', ' ', html_content)
            text = html.unescape(text)
            text = re.sub(r'\s+', ' ', text)
            return text.strip()

    def _extract_all_fields(self, text: str) -> Dict[str, Any]:
        """Extract all company data fields from text."""
        data = {}

        # Extract each field type
        data.update(self._extract_company_name(text))
        data.update(self._extract_legal_structure(text))
        data.update(self._extract_address(text))
        data.update(self._extract_contact_info(text))
        data.update(self._extract_management(text))
        data.update(self._extract_registration(text))
        data.update(self._extract_tax_info(text))

        return data

    def _extract_company_name(self, text: str) -> Dict[str, str]:
        """Extract company name from text."""
        for pattern in self.company_patterns:
            match = pattern.search(text)
            if match:
                name = match.group(1).strip()
                if len(name) > 3:  # Minimum reasonable length
                    return {"company_name": name}
        return {"company_name": ""}

    def _extract_legal_structure(self, text: str) -> Dict[str, str]:
        """Extract legal structure from text."""
        for pattern in self.legal_structure_patterns:
            match = pattern.search(text)
            if match:
                structure = match.group(1).upper()
                # Map to enum values
                structure_mapping = {
                    "GMBH": LegalStructure.GMBH,
                    "AG": LegalStructure.AG,
                    "E.K.": LegalStructure.EK,
                    "KG": LegalStructure.KG,
                    "OHG": LegalStructure.OHG,
                    "UG": LegalStructure.UG,
                    "GBR": LegalStructure.GBR,
                    "LLC": LegalStructure.LLC,
                    "INC": LegalStructure.INC,
                    "LTD": LegalStructure.LTD,
                    "CORP": LegalStructure.CORP,
                }
                return {"legal_structure": structure_mapping.get(structure, LegalStructure.OTHER)}
        return {"legal_structure": LegalStructure.UNKNOWN}

    def _extract_address(self, text: str) -> Dict[str, str]:
        """Extract address components from text."""
        result = {"street_address": "", "zip_code": "",
                  "city": "", "country": Country.UNKNOWN}

        # Try address patterns
        for pattern in self.address_patterns:
            match = pattern.search(text)
            if match:
                if len(match.groups()) == 1:
                    # Full address line
                    address_line = match.group(1).strip()
                    result["street_address"] = address_line
                else:
                    # Street and number
                    street = match.group(1).strip()
                    number = match.group(2).strip()
                    result["street_address"] = f"{street} {number}"
                break

        # Try ZIP code and city patterns
        for pattern in self.zip_city_patterns:
            match = pattern.search(text)
            if match:
                result["zip_code"] = match.group(1).strip()
                result["city"] = match.group(2).strip()
                break

        # Detect country (simple heuristics)
        if "deutschland" in text.lower() or "germany" in text.lower():
            result["country"] = Country.GERMANY
        elif "österreich" in text.lower() or "austria" in text.lower():
            result["country"] = Country.AUSTRIA
        elif "schweiz" in text.lower() or "switzerland" in text.lower():
            result["country"] = Country.SWITZERLAND

        return result

    def _extract_contact_info(self, text: str) -> Dict[str, str]:
        """Extract contact information from text."""
        result = {"phone": "", "email": "", "website": ""}

        # Extract phone
        for pattern in self.phone_patterns:
            match = pattern.search(text)
            if match:
                phone = match.group(1).strip() if len(
                    match.groups()) > 0 else match.group(0).strip()
                result["phone"] = phone
                break

        # Extract email
        for pattern in self.email_patterns:
            match = pattern.search(text)
            if match:
                email = match.group(1).strip() if len(
                    match.groups()) > 0 else match.group(0).strip()
                result["email"] = email
                break

        # Extract website
        for pattern in self.website_patterns:
            match = pattern.search(text)
            if match:
                website = match.group(1).strip() if len(
                    match.groups()) > 0 else match.group(0).strip()
                if not website.startswith('http'):
                    website = 'https://' + website
                result["website"] = website
                break

        return result

    def _extract_management(self, text: str) -> Dict[str, str]:
        """Extract management information from text."""
        result = {"managing_director": "", "management_title": ""}

        for pattern in self.director_patterns:
            match = pattern.search(text)
            if match:
                director_info = match.group(1).strip()
                result["managing_director"] = director_info

                # Try to extract title
                if "geschäftsführer" in text.lower():
                    result["management_title"] = "Geschäftsführer"
                elif "ceo" in text.lower():
                    result["management_title"] = "CEO"
                elif "inhaber" in text.lower():
                    result["management_title"] = "Inhaber"
                break

        return result

    def _extract_registration(self, text: str) -> Dict[str, str]:
        """Extract registration information from text."""
        result = {"registration_number": "", "register_court": ""}

        # Extract registration number
        for pattern in self.registration_patterns:
            match = pattern.search(text)
            if match:
                reg_number = match.group(1).strip()
                result["registration_number"] = reg_number
                break

        # Extract court
        for pattern in self.court_patterns:
            match = pattern.search(text)
            if match:
                court = match.group(1).strip()
                result["register_court"] = court
                break

        return result

    def _extract_tax_info(self, text: str) -> Dict[str, str]:
        """Extract tax information from text."""
        result = {"tax_id": "", "vat_id": ""}

        for pattern in self.tax_patterns:
            match = pattern.search(text)
            if match:
                tax_info = match.group(1).strip()

                # Determine if it's VAT ID or tax number
                if any(keyword in pattern.pattern.lower() for keyword in ["ust", "vat", "umsatzsteuer"]):
                    result["vat_id"] = tax_info
                else:
                    result["tax_id"] = tax_info

        return result

    def _calculate_confidence(self, data: Dict[str, Any]) -> float:
        """
        Calculate confidence score based on extracted data quality.

        Args:
            data: Extracted data dictionary

        Returns:
            Confidence score between 0.0 and 1.0
        """
        score = 0.0
        max_score = 0.0

        # Weight different fields by importance
        field_weights = {
            "company_name": 0.25,
            "street_address": 0.15,
            "city": 0.10,
            "email": 0.15,
            "phone": 0.10,
            "managing_director": 0.10,
            "registration_number": 0.10,
            "vat_id": 0.05
        }

        for field, weight in field_weights.items():
            max_score += weight
            if field in data and data[field] and str(data[field]).strip():
                score += weight

        # Bonus for having legal structure
        if data.get("legal_structure") and data["legal_structure"] != LegalStructure.UNKNOWN:
            score += 0.05
            max_score += 0.05

        return min(score / max_score if max_score > 0 else 0.0, 1.0)
