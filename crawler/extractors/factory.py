"""
Factory for creating and managing company data extractors.

This module provides a factory pattern for creating extractors and
managing the fallback logic between different extraction strategies.
"""

from enum import Enum
from typing import Optional, Dict, Any

from .base import CompanyDataExtractor, ExtractionResult
from .regex_extractor import RegexCompanyDataExtractor
from .llm_extractor import LLMCompanyDataExtractor
from utils.logger import logger


class ExtractorType(str, Enum):
    """Types of available extractors."""
    REGEX = "regex"
    LLM = "llm"
    AUTO = "auto"  # Automatic selection with fallback


class ExtractorFactory:
    """
    Factory for creating and managing company data extractors.

    Provides methods to create extractors, manage fallback logic,
    and optimize extraction strategies based on performance.
    """

    def __init__(self):
        """Initialize the factory with default settings."""
        self._extractors: Dict[str, CompanyDataExtractor] = {}
        self._performance_stats: Dict[str, Dict[str, float]] = {}

    def create_extractor(
        self,
        extractor_type: ExtractorType,
        **kwargs
    ) -> CompanyDataExtractor:
        """
        Create an extractor of the specified type.

        Args:
            extractor_type: Type of extractor to create
            **kwargs: Additional arguments for extractor initialization

        Returns:
            Configured extractor instance
        """
        if extractor_type == ExtractorType.REGEX:
            return RegexCompanyDataExtractor()
        elif extractor_type == ExtractorType.LLM:
            model = kwargs.get('model', 'qwen3')
            temperature = kwargs.get('temperature', 0.1)
            return LLMCompanyDataExtractor(model=model, temperature=temperature)
        else:
            raise ValueError(f"Unknown extractor type: {extractor_type}")

    def get_or_create_extractor(
        self,
        extractor_type: ExtractorType,
        **kwargs
    ) -> CompanyDataExtractor:
        """
        Get existing extractor or create new one if not exists.

        Args:
            extractor_type: Type of extractor
            **kwargs: Additional arguments for extractor initialization

        Returns:
            Extractor instance (cached or newly created)
        """
        cache_key = f"{extractor_type.value}_{hash(str(sorted(kwargs.items())))}"

        if cache_key not in self._extractors:
            self._extractors[cache_key] = self.create_extractor(
                extractor_type, **kwargs)

        return self._extractors[cache_key]

    async def extract_with_fallback(
        self,
        html_content: str,
        source_url: str = "",
        primary_type: ExtractorType = ExtractorType.REGEX,
        fallback_type: ExtractorType = ExtractorType.LLM,
        min_confidence_threshold: float = 0.3,
        **extractor_kwargs
    ) -> ExtractionResult:
        """
        Extract company data with automatic fallback between extractors.

        Args:
            html_content: HTML content to extract from
            source_url: Source URL for metadata
            primary_type: Primary extractor type to try first
            fallback_type: Fallback extractor type if primary fails
            min_confidence_threshold: Minimum confidence to accept primary result
            **extractor_kwargs: Additional arguments for extractors

        Returns:
            Best extraction result from primary or fallback extractor
        """
        results = []

        # Try primary extractor
        try:
            primary_extractor = self.get_or_create_extractor(
                primary_type, **extractor_kwargs)
            primary_result = await primary_extractor.extract(html_content, source_url)
            results.append(primary_result)

            logger.info(
                f"Primary extraction ({primary_type.value}) completed: "
                f"success={primary_result.success}, confidence={primary_result.confidence_score:.2f}"
            )

            # If primary result is good enough, return it
            if (primary_result.success and
                    primary_result.confidence_score >= min_confidence_threshold):
                return primary_result

        except Exception as e:
            logger.warning(
                f"Primary extractor ({primary_type.value}) failed: {e}")

        # Try fallback extractor if primary failed or confidence too low
        if fallback_type != primary_type:
            try:
                fallback_extractor = self.get_or_create_extractor(
                    fallback_type, **extractor_kwargs)
                fallback_result = await fallback_extractor.extract(html_content, source_url)
                results.append(fallback_result)

                logger.info(
                    f"Fallback extraction ({fallback_type.value}) completed: "
                    f"success={fallback_result.success}, confidence={fallback_result.confidence_score:.2f}"
                )

            except Exception as e:
                logger.warning(
                    f"Fallback extractor ({fallback_type.value}) failed: {e}")

        # Return best result
        if results:
            best_result = max(
                results, key=lambda r: r.confidence_score if r.success else 0.0)
            return best_result
        else:
            # Return empty result if all extractors failed
            return ExtractionResult(
                data={},
                success=False,
                confidence_score=0.0,
                extraction_method="failed",
                processing_time_ms=0,
                source_url=source_url,
                error_message="All extractors failed"
            )

    async def extract_with_auto_selection(
        self,
        html_content: str,
        source_url: str = "",
        **extractor_kwargs
    ) -> ExtractionResult:
        """
        Extract company data with automatic extractor selection.

        Uses performance statistics and content analysis to choose
        the best extraction strategy.

        Args:
            html_content: HTML content to extract from
            source_url: Source URL for metadata
            **extractor_kwargs: Additional arguments for extractors

        Returns:
            Extraction result from automatically selected extractor
        """
        # Analyze content to determine best strategy
        content_analysis = self._analyze_content(html_content)

        # Choose strategy based on content characteristics
        if content_analysis['is_simple'] and content_analysis['has_clear_patterns']:
            # Use regex for simple, well-structured content
            primary_type = ExtractorType.REGEX
            fallback_type = ExtractorType.LLM
            min_confidence = 0.4
        else:
            # Use LLM for complex or poorly structured content
            primary_type = ExtractorType.LLM
            fallback_type = ExtractorType.REGEX
            min_confidence = 0.3

        logger.info(
            f"Auto-selected extraction strategy: primary={primary_type.value}, "
            f"fallback={fallback_type.value} (content_score={content_analysis['complexity_score']:.2f})"
        )

        return await self.extract_with_fallback(
            html_content=html_content,
            source_url=source_url,
            primary_type=primary_type,
            fallback_type=fallback_type,
            min_confidence_threshold=min_confidence,
            **extractor_kwargs
        )

    def _analyze_content(self, html_content: str) -> Dict[str, Any]:
        """
        Analyze HTML content to determine extraction strategy.

        Args:
            html_content: HTML content to analyze

        Returns:
            Dictionary with content analysis results
        """
        from bs4 import BeautifulSoup
        import re

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            text = soup.get_text().lower()

            # Check for clear patterns
            pattern_indicators = [
                r'geschäftsführer:\s*[a-zäöüß\s]+',
                r'telefon:\s*[\d\s\-\+\(\)]+',
                r'e-?mail:\s*[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}',
                r'handelsregister:\s*[a-z0-9\s]+',
                r'\d{5}\s+[a-zäöüß\s]+',  # German ZIP + city
            ]

            pattern_matches = sum(1 for pattern in pattern_indicators
                                  if re.search(pattern, text))

            # Calculate complexity metrics
            text_length = len(text)
            line_count = text.count('\n')
            avg_line_length = text_length / max(line_count, 1)

            # Determine if content is simple and well-structured
            is_simple = (
                text_length < 2000 and
                pattern_matches >= 2 and
                avg_line_length < 100
            )

            has_clear_patterns = pattern_matches >= 3
            complexity_score = min(
                text_length / 1000.0 + (5 - pattern_matches) * 0.2, 1.0)

            return {
                'is_simple': is_simple,
                'has_clear_patterns': has_clear_patterns,
                'complexity_score': complexity_score,
                'text_length': text_length,
                'pattern_matches': pattern_matches
            }

        except Exception as e:
            logger.warning(f"Content analysis failed: {e}")
            return {
                'is_simple': False,
                'has_clear_patterns': False,
                'complexity_score': 0.8,
                'text_length': len(html_content),
                'pattern_matches': 0
            }

    def get_performance_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get performance statistics for all extractors.

        Returns:
            Dictionary with performance metrics for each extractor
        """
        stats = {}
        for key, extractor in self._extractors.items():
            stats[key] = extractor.get_stats()
        return stats

    def clear_cache(self):
        """Clear the extractor cache."""
        self._extractors.clear()
        self._performance_stats.clear()


# Global factory instance
_factory_instance: Optional[ExtractorFactory] = None


def get_factory() -> ExtractorFactory:
    """
    Get the global extractor factory instance.

    Returns:
        Global ExtractorFactory instance
    """
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = ExtractorFactory()
    return _factory_instance
