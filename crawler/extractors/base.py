"""
Base classes and interfaces for company data extractors.

This module defines the common interface that all extractors must implement
and shared data structures for extraction results.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import time

from crawler.models import CompanyData


@dataclass
class ExtractionResult:
    """
    Result of a company data extraction operation.

    Contains the extracted data, metadata about the extraction process,
    and information about success/failure.
    """

    # Extraction results
    data: Dict[str, Any]
    success: bool
    confidence_score: float

    # Metadata
    extraction_method: str
    processing_time_ms: int
    source_url: Optional[str] = None
    error_message: Optional[str] = None

    # Raw data for debugging
    raw_content_length: int = 0
    processed_content_length: int = 0

    def to_company_data(self, source_url: str = "") -> CompanyData:
        """
        Convert extraction result to CompanyData model.

        Args:
            source_url: URL where the data was extracted from

        Returns:
            CompanyData instance with validated and structured data
        """
        # Prepare data with metadata
        data_with_metadata = self.data.copy()
        data_with_metadata.update({
            'extraction_method': self.extraction_method,
            'confidence_score': self.confidence_score,
            'source_url': source_url or self.source_url or "",
            'extraction_timestamp': datetime.now().isoformat()
        })

        # Create and return CompanyData instance
        return CompanyData(**data_with_metadata)

    def is_valid(self) -> bool:
        """Check if the extraction result is valid and usable."""
        return (
            self.success and
            self.confidence_score > 0.0 and
            isinstance(self.data, dict) and
            len(self.data) > 0
        )


class CompanyDataExtractor(ABC):
    """
    Abstract base class for company data extractors.

    All extractors must implement the extract method with a consistent interface.
    """

    def __init__(self, name: str):
        """
        Initialize the extractor.

        Args:
            name: Human-readable name for this extractor
        """
        self.name = name
        self._extraction_count = 0
        self._total_processing_time = 0.0

    @abstractmethod
    async def extract(self, html_content: str, source_url: str = "") -> ExtractionResult:
        """
        Extract company data from HTML content.

        Args:
            html_content: Raw HTML content to extract data from
            source_url: Optional URL where the content was retrieved from

        Returns:
            ExtractionResult containing extracted data and metadata
        """
        pass

    def get_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for this extractor.

        Returns:
            Dictionary with performance metrics
        """
        avg_time = (
            self._total_processing_time / self._extraction_count
            if self._extraction_count > 0 else 0.0
        )

        return {
            'name': self.name,
            'extraction_count': self._extraction_count,
            'total_processing_time_ms': self._total_processing_time,
            'average_processing_time_ms': avg_time
        }

    def _start_timing(self) -> float:
        """Start timing an extraction operation."""
        return time.time()

    def _end_timing(self, start_time: float) -> int:
        """
        End timing an extraction operation and update stats.

        Args:
            start_time: Start time from _start_timing()

        Returns:
            Processing time in milliseconds
        """
        processing_time = (time.time() - start_time) * 1000
        self._extraction_count += 1
        self._total_processing_time += processing_time
        return int(processing_time)

    def _create_result(
        self,
        data: Dict[str, Any],
        success: bool,
        confidence_score: float,
        processing_time_ms: int,
        source_url: str = "",
        error_message: str = "",
        raw_content_length: int = 0,
        processed_content_length: int = 0
    ) -> ExtractionResult:
        """
        Create a standardized ExtractionResult.

        Args:
            data: Extracted company data
            success: Whether extraction was successful
            confidence_score: Confidence in extraction quality (0.0-1.0)
            processing_time_ms: Time taken for extraction
            source_url: Source URL
            error_message: Error message if extraction failed
            raw_content_length: Length of original content
            processed_content_length: Length of processed content

        Returns:
            ExtractionResult instance
        """
        return ExtractionResult(
            data=data,
            success=success,
            confidence_score=confidence_score,
            extraction_method=self.name,
            processing_time_ms=processing_time_ms,
            source_url=source_url,
            error_message=error_message if error_message else None,
            raw_content_length=raw_content_length,
            processed_content_length=processed_content_length
        )

    def __str__(self) -> str:
        """String representation of the extractor."""
        return f"{self.__class__.__name__}(name='{self.name}')"

    def __repr__(self) -> str:
        """Detailed string representation of the extractor."""
        stats = self.get_stats()
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"extractions={stats['extraction_count']}, "
            f"avg_time={stats['average_processing_time_ms']:.1f}ms"
            f")"
        )
